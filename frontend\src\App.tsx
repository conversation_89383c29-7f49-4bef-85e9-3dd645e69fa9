import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import SensorDetail from './pages/SensorDetail';
import SensorMap from './pages/SensorMap';
import FusionRules from './pages/FusionRules';
import Insights from './pages/Insights';
import Settings from './pages/Settings';
import NotFound from './pages/NotFound';

const App: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<Layout />}>
        <Route index element={<Dashboard />} />
        <Route path="sensors/:sensorId" element={<SensorDetail />} />
        <Route path="sensor-map" element={<SensorMap />} />
        <Route path="fusion-rules" element={<FusionRules />} />
        <Route path="insights" element={<Insights />} />
        <Route path="settings" element={<Settings />} />
        <Route path="*" element={<NotFound />} />
      </Route>
    </Routes>
  );
};

export default App;
