"""MQTT client for ingesting sensor data."""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Set, Union

import paho.mqtt.client as mqtt
from pydantic import ValidationError

from enviromesh.core.models import SensorReading, SensorType
from enviromesh.core.storage import TimeSeriesStorage

logger = logging.getLogger(__name__)


class MQTTClient:
    """MQTT client for ingesting sensor data."""

    def __init__(
        self,
        broker: str,
        port: int = 1883,
        username: Optional[str] = None,
        password: Optional[str] = None,
        client_id: str = "enviromesh-client",
        storage: Optional[TimeSeriesStorage] = None,
    ) -> None:
        """Initialize the MQTT client.

        Args:
            broker: The MQTT broker address.
            port: The MQTT broker port.
            username: Optional username for authentication.
            password: Optional password for authentication.
            client_id: The client ID to use.
            storage: Optional storage for persisting readings.
        """
        self.broker = broker
        self.port = port
        self.username = username
        self.password = password
        self.client_id = client_id
        self.storage = storage
        self.client = mqtt.Client(client_id=client_id)
        self.subscribed_topics: Set[str] = set()
        self.callbacks: Dict[str, List[Callable[[SensorReading], None]]] = {}
        self.connected = False

        # Set up callbacks
        self.client.on_connect = self._on_connect
        self.client.on_message = self._on_message
        self.client.on_disconnect = self._on_disconnect

        # Set up authentication if provided
        if username and password:
            self.client.username_pw_set(username, password)

    def connect(self) -> None:
        """Connect to the MQTT broker."""
        try:
            self.client.connect(self.broker, self.port, 60)
            self.client.loop_start()
            logger.info(f"Connected to MQTT broker at {self.broker}:{self.port}")
        except Exception as e:
            logger.error(f"Failed to connect to MQTT broker: {e}")
            raise

    def disconnect(self) -> None:
        """Disconnect from the MQTT broker."""
        self.client.loop_stop()
        self.client.disconnect()
        self.connected = False
        logger.info("Disconnected from MQTT broker")

    def subscribe(self, topic: str) -> None:
        """Subscribe to a topic.

        Args:
            topic: The topic to subscribe to.
        """
        self.client.subscribe(topic)
        self.subscribed_topics.add(topic)
        logger.info(f"Subscribed to topic: {topic}")

    def unsubscribe(self, topic: str) -> None:
        """Unsubscribe from a topic.

        Args:
            topic: The topic to unsubscribe from.
        """
        self.client.unsubscribe(topic)
        self.subscribed_topics.remove(topic)
        logger.info(f"Unsubscribed from topic: {topic}")

    def register_callback(
        self, topic: str, callback: Callable[[SensorReading], None]
    ) -> None:
        """Register a callback for a topic.

        Args:
            topic: The topic to register the callback for.
            callback: The callback function to call when a message is received.
        """
        if topic not in self.callbacks:
            self.callbacks[topic] = []
        self.callbacks[topic].append(callback)
        logger.info(f"Registered callback for topic: {topic}")

    def _on_connect(
        self, client: mqtt.Client, userdata: Any, flags: Dict[str, int], rc: int
    ) -> None:
        """Callback for when the client connects to the broker.

        Args:
            client: The client instance.
            userdata: User data of any type.
            flags: Response flags sent by the broker.
            rc: The connection result.
        """
        if rc == 0:
            self.connected = True
            logger.info("Connected to MQTT broker")
            # Resubscribe to topics
            for topic in self.subscribed_topics:
                client.subscribe(topic)
        else:
            logger.error(f"Failed to connect to MQTT broker with code {rc}")

    def _on_disconnect(
        self, client: mqtt.Client, userdata: Any, rc: int
    ) -> None:
        """Callback for when the client disconnects from the broker.

        Args:
            client: The client instance.
            userdata: User data of any type.
            rc: The disconnection result.
        """
        self.connected = False
        if rc != 0:
            logger.warning(f"Unexpected disconnection from MQTT broker with code {rc}")
        else:
            logger.info("Disconnected from MQTT broker")

    def _on_message(
        self, client: mqtt.Client, userdata: Any, msg: mqtt.MQTTMessage
    ) -> None:
        """Callback for when a message is received from the broker.

        Args:
            client: The client instance.
            userdata: User data of any type.
            msg: The received message.
        """
        topic = msg.topic
        payload = msg.payload.decode("utf-8")
        logger.debug(f"Received message on topic {topic}: {payload}")

        try:
            # Parse the payload as JSON
            data = json.loads(payload)

            # Try to convert the data to a SensorReading
            reading = self._parse_sensor_reading(data, topic)

            # Store the reading if storage is available
            if self.storage:
                asyncio.create_task(self.storage.store_sensor_reading(reading))

            # Call registered callbacks
            if topic in self.callbacks:
                for callback in self.callbacks[topic]:
                    callback(reading)

        except json.JSONDecodeError:
            logger.error(f"Failed to parse message as JSON: {payload}")
        except ValidationError as e:
            logger.error(f"Failed to validate sensor reading: {e}")
        except Exception as e:
            logger.error(f"Error processing message: {e}")

    def _parse_sensor_reading(self, data: Dict[str, Any], topic: str) -> SensorReading:
        """Parse a sensor reading from a JSON payload.

        Args:
            data: The JSON data.
            topic: The MQTT topic.

        Returns:
            A SensorReading object.

        Raises:
            ValidationError: If the data is invalid.
        """
        # Extract sensor_id and sensor_type from the topic if not in the data
        # Example topic format: sensors/air_quality/sensor123
        if "sensor_id" not in data or "sensor_type" not in data:
            parts = topic.split("/")
            if len(parts) >= 3:
                if "sensor_type" not in data:
                    data["sensor_type"] = parts[1]
                if "sensor_id" not in data:
                    data["sensor_id"] = parts[2]

        # Convert timestamp string to datetime if needed
        if "timestamp" in data and isinstance(data["timestamp"], str):
            try:
                data["timestamp"] = datetime.fromisoformat(data["timestamp"])
            except ValueError:
                # If the timestamp is not in ISO format, try a few common formats
                for fmt in ["%Y-%m-%d %H:%M:%S", "%Y/%m/%d %H:%M:%S", "%d/%m/%Y %H:%M:%S"]:
                    try:
                        data["timestamp"] = datetime.strptime(data["timestamp"], fmt)
                        break
                    except ValueError:
                        continue
                else:
                    # If none of the formats match, use the current time
                    data["timestamp"] = datetime.utcnow()

        # Create the SensorReading object
        return SensorReading(**data)
