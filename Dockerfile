FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install FEniCSx dependencies
RUN apt-get update && apt-get install -y \
    cmake \
    pkg-config \
    libopenmpi-dev \
    liblapack-dev \
    libpetsc-real-dev \
    libslepc-real-dev \
    python3-petsc4py \
    python3-slepc4py \
    python3-mpi4py \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY pyproject.toml .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir .

# Copy the rest of the application
COPY . .

# Expose the API port
EXPOSE 8000

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Run the application
CMD ["uvicorn", "enviromesh.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
