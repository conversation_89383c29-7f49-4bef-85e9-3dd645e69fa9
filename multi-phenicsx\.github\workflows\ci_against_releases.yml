name: multiphenicsx CI (against releases)

on:
  schedule:
    - cron: "0 3 * * *"
  workflow_dispatch:

jobs:
  test_dolfinx_v0_4_1:
    uses: multiphenics/multiphenicsx/.github/workflows/ci.yml@dolfinx-v0.4.1

  test_dolfinx_v0_5_2:
    uses: multiphenics/multiphenicsx/.github/workflows/ci.yml@dolfinx-v0.5.2

  test_dolfinx_v0_6_0:
    uses: multiphenics/multiphenicsx/.github/workflows/ci.yml@dolfinx-v0.6.0

  test_dolfinx_v0_7_0:
    uses: multiphenics/multiphenicsx/.github/workflows/ci.yml@dolfinx-v0.7.0

  test_dolfinx_v0_7_1:
    uses: multiphenics/multiphenicsx/.github/workflows/ci.yml@dolfinx-v0.7.1

  test_dolfinx_v0_7_2:
    uses: multiphenics/multiphenicsx/.github/workflows/ci.yml@dolfinx-v0.7.2

  test_dolfinx_v0_7_3:
    uses: multiphenics/multiphenicsx/.github/workflows/ci.yml@dolfinx-v0.7.3

  test_dolfinx_v0_8_0:
    uses: multiphenics/multiphenicsx/.github/workflows/ci.yml@dolfinx-v0.8.0

  test_dolfinx_v0_9_0:
    uses: multiphenics/multiphenicsx/.github/workflows/ci.yml@dolfinx-v0.9.0

  warn:
    runs-on: ubuntu-latest
    if: github.repository == 'multiphenics/multiphenicsx' && github.ref == 'refs/heads/main' && github.event_name == 'schedule'
    steps:
      - name: Warn if scheduled workflow is about to be disabled
        uses: fem-on-colab/warn-workflow-about-to-be-disabled-action@main
        with:
          workflow-filename: ci_against_releases.yml
          days-elapsed: 55
