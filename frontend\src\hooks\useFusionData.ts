import { useState, useEffect } from 'react';
import axios from 'axios';

interface FusionRule {
  name: string;
  description?: string;
  operator: string;
  inputs: string[];
  weights?: number[];
  parameters?: Record<string, any>;
}

interface UseFusionDataResult {
  fusionData: FusionRule[] | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
}

export const useFusionData = (): UseFusionDataResult => {
  const [fusionData, setFusionData] = useState<FusionRule[] | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [refetchIndex, setRefetchIndex] = useState<number>(0);

  const refetch = () => {
    setRefetchIndex(prev => prev + 1);
  };

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const response = await axios.get('/api/mesh/rules');
        setFusionData(response.data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('An unknown error occurred'));
        setFusionData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [refetchIndex]);

  return { fusionData, isLoading, error, refetch };
};
