import React, { useState } from 'react';
import { format } from 'date-fns';
import axios from 'axios';
import InsightCard from '../components/InsightCard';

interface Insight {
  mesh_id: string;
  timestamp: string;
  text: string;
  confidence: number;
}

const Insights: React.FC = () => {
  const [insights, setInsights] = useState<Insight[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    mesh_id: 'default',
    time_range: '24h',
    question: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await axios.post('/api/insight', formData);
      setInsights(prev => [response.data, ...prev]);
    } catch (err) {
      setError('Failed to generate insight. Please try again.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  // Sample insights for demonstration
  const sampleInsights = [
    {
      mesh_id: 'default',
      timestamp: new Date().toISOString(),
      text: 'The urban comfort index shows a 15% improvement over the last 24 hours, primarily driven by reduced noise levels and improved air quality. Traffic remains consistent with previous patterns.',
      confidence: 0.85,
    },
    {
      mesh_id: 'default',
      timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
      text: 'Air quality sensors are showing elevated PM2.5 levels in the downtown area, correlating with increased traffic density. Recommend increased monitoring and potential traffic management interventions.',
      confidence: 0.92,
    },
    {
      mesh_id: 'default',
      timestamp: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
      text: 'Noise levels have decreased by 8dB on average across all monitoring stations, likely due to the recent implementation of noise reduction measures in high-traffic areas.',
      confidence: 0.78,
    },
  ];

  return (
    <div>
      <h1 className="text-2xl font-semibold text-gray-900">AI Insights</h1>
      
      {/* Insight Generator Form */}
      <div className="mt-6 bg-white shadow sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Generate New Insight</h3>
          <div className="mt-2 max-w-xl text-sm text-gray-500">
            <p>Ask a question about your environmental data to get AI-generated insights.</p>
          </div>
          {error && (
            <div className="mt-2 text-sm text-red-600">
              {error}
            </div>
          )}
          <form className="mt-5 sm:flex sm:items-end" onSubmit={handleSubmit}>
            <div className="w-full sm:max-w-xs mr-2">
              <label htmlFor="mesh_id" className="block text-sm font-medium text-gray-700">
                Mesh ID
              </label>
              <select
                id="mesh_id"
                name="mesh_id"
                value={formData.mesh_id}
                onChange={handleInputChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              >
                <option value="default">Default Mesh</option>
                <option value="downtown">Downtown</option>
                <option value="residential">Residential Area</option>
              </select>
            </div>
            <div className="w-full sm:max-w-xs mr-2 mt-3 sm:mt-0">
              <label htmlFor="time_range" className="block text-sm font-medium text-gray-700">
                Time Range
              </label>
              <select
                id="time_range"
                name="time_range"
                value={formData.time_range}
                onChange={handleInputChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              >
                <option value="1h">Last Hour</option>
                <option value="6h">Last 6 Hours</option>
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
              </select>
            </div>
            <div className="w-full mt-3 sm:mt-0">
              <button
                type="submit"
                disabled={isLoading}
                className="mt-3 w-full inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                {isLoading ? 'Generating...' : 'Generate Insight'}
              </button>
            </div>
          </form>
          <div className="mt-3">
            <label htmlFor="question" className="block text-sm font-medium text-gray-700">
              Specific Question (Optional)
            </label>
            <textarea
              id="question"
              name="question"
              rows={2}
              value={formData.question}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="What trends do you see in air quality over the selected time period?"
            />
          </div>
        </div>
      </div>

      {/* Insights List */}
      <div className="mt-8 space-y-6 mb-12">
        <h2 className="text-lg font-medium text-gray-900">Recent Insights</h2>
        
        {/* Generated insights */}
        {insights.map((insight, index) => (
          <InsightCard
            key={`generated-${index}`}
            title={`Insight for ${insight.mesh_id}`}
            content={insight.text}
            date={new Date(insight.timestamp)}
            confidence={insight.confidence}
          />
        ))}
        
        {/* Sample insights */}
        {sampleInsights.map((insight, index) => (
          <InsightCard
            key={`sample-${index}`}
            title={`Insight for ${insight.mesh_id}`}
            content={insight.text}
            date={new Date(insight.timestamp)}
            confidence={insight.confidence}
          />
        ))}
        
        {insights.length === 0 && sampleInsights.length === 0 && (
          <p className="text-gray-500">No insights available. Generate one to get started.</p>
        )}
      </div>
    </div>
  );
};

export default Insights;
