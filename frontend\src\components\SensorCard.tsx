import React from 'react';
import { Link } from 'react-router-dom';
import { format } from 'date-fns';
import { 
  SignalIcon, 
  SpeakerWaveIcon, 
  TruckIcon, 
  SunIcon 
} from '@heroicons/react/24/outline';

interface SensorCardProps {
  type: string;
  count: number;
  latestReading: any;
}

const SensorCard: React.FC<SensorCardProps> = ({ type, count, latestReading }) => {
  // Get the appropriate icon based on sensor type
  const getIcon = () => {
    switch (type) {
      case 'air_quality':
        return <SignalIcon className="h-6 w-6 text-blue-500" />;
      case 'noise':
        return <SpeakerWaveIcon className="h-6 w-6 text-purple-500" />;
      case 'traffic':
        return <TruckIcon className="h-6 w-6 text-green-500" />;
      case 'light':
        return <SunIcon className="h-6 w-6 text-yellow-500" />;
      default:
        return <SignalIcon className="h-6 w-6 text-gray-500" />;
    }
  };

  // Format the sensor type for display
  const formatSensorType = (type: string) => {
    return type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">
                {formatSensorType(type)} Sensors
              </dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">
                  {count}
                </div>
                <div className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                  <span className="sr-only">Active</span>
                </div>
              </dd>
            </dl>
          </div>
        </div>
      </div>
      {latestReading && (
        <div className="bg-gray-50 px-4 py-4 sm:px-6">
          <div className="text-sm">
            <div className="font-medium text-gray-900">Latest Reading</div>
            <div className="mt-1 text-gray-500">
              {latestReading.value} {latestReading.unit}
            </div>
            <div className="mt-1 text-xs text-gray-500">
              {latestReading.timestamp ? format(new Date(latestReading.timestamp), 'MMM d, yyyy HH:mm:ss') : 'No timestamp'}
            </div>
          </div>
          <div className="mt-2 text-sm">
            <Link
              to={`/sensors/${latestReading.sensor_id}`}
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              View details
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default SensorCard;
