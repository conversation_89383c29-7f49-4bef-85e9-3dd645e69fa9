"""API routes for real-time mesh data."""

from datetime import datetime
from typing import Dict, List, Optional

from fastapi import <PERSON><PERSON>outer, Depends, HTTPException, Query, Request
from pydantic import BaseModel

from enviromesh.core.fusion import FusionEngine
from enviromesh.core.models import (
    FusionResult,
    FusionRule,
    MeshNode,
    MeshQuery,
    SensorReading,
    SensorType,
)
from enviromesh.core.storage import TimeSeriesStorage

router = APIRouter()


def get_storage(request: Request) -> TimeSeriesStorage:
    """Get the storage from the request state.

    Args:
        request: The request object.

    Returns:
        The storage instance.
    """
    return request.app.state.storage


def get_fusion_engine(request: Request) -> FusionEngine:
    """Get the fusion engine from the request state.

    Args:
        request: The request object.

    Returns:
        The fusion engine instance.
    """
    return request.app.state.fusion_engine


class MeshResponse(BaseModel):
    """Response model for mesh data."""

    sensor_readings: List[SensorReading]
    fusion_results: List[FusionResult]


@router.get("/", response_model=MeshResponse)
async def get_mesh_data(
    request: Request,
    node_ids: Optional[List[str]] = Query(None),
    sensor_types: Optional[List[SensorType]] = Query(None),
    fusion_rules: Optional[List[str]] = Query(None),
    start_time: Optional[datetime] = Query(None),
    end_time: Optional[datetime] = Query(None),
    limit: int = Query(100, ge=1, le=1000),
    storage: TimeSeriesStorage = Depends(get_storage),
    fusion_engine: FusionEngine = Depends(get_fusion_engine),
) -> MeshResponse:
    """Get real-time mesh data.

    Args:
        request: The request object.
        node_ids: Optional list of node IDs to filter by.
        sensor_types: Optional list of sensor types to filter by.
        fusion_rules: Optional list of fusion rules to filter by.
        start_time: Optional start time to filter by.
        end_time: Optional end time to filter by.
        limit: Maximum number of results to return.
        storage: The storage instance.
        fusion_engine: The fusion engine instance.

    Returns:
        A MeshResponse object containing sensor readings and fusion results.
    """
    # Get sensor readings
    sensor_readings = await storage.get_sensor_readings(
        sensor_ids=node_ids,
        sensor_types=sensor_types,
        start_time=start_time,
        end_time=end_time,
        limit=limit,
    )

    # Get fusion results
    fusion_results = await storage.get_fusion_results(
        rule_names=fusion_rules,
        start_time=start_time,
        end_time=end_time,
        limit=limit,
    )

    return MeshResponse(
        sensor_readings=sensor_readings,
        fusion_results=fusion_results,
    )


@router.get("/rules", response_model=List[FusionRule])
async def get_fusion_rules(
    request: Request,
    fusion_engine: FusionEngine = Depends(get_fusion_engine),
) -> List[FusionRule]:
    """Get all registered fusion rules.

    Args:
        request: The request object.
        fusion_engine: The fusion engine instance.

    Returns:
        A list of fusion rules.
    """
    return fusion_engine.list_rules()


@router.get("/rules/{rule_name}", response_model=FusionRule)
async def get_fusion_rule(
    rule_name: str,
    request: Request,
    fusion_engine: FusionEngine = Depends(get_fusion_engine),
) -> FusionRule:
    """Get a fusion rule by name.

    Args:
        rule_name: The name of the rule to get.
        request: The request object.
        fusion_engine: The fusion engine instance.

    Returns:
        The fusion rule.

    Raises:
        HTTPException: If the rule does not exist.
    """
    try:
        return fusion_engine.get_rule(rule_name)
    except KeyError:
        raise HTTPException(status_code=404, detail=f"Fusion rule not found: {rule_name}")


@router.post("/rules", response_model=FusionRule)
async def create_fusion_rule(
    rule: FusionRule,
    request: Request,
    fusion_engine: FusionEngine = Depends(get_fusion_engine),
) -> FusionRule:
    """Create a new fusion rule.

    Args:
        rule: The fusion rule to create.
        request: The request object.
        fusion_engine: The fusion engine instance.

    Returns:
        The created fusion rule.

    Raises:
        HTTPException: If a rule with the same name already exists.
    """
    try:
        # Check if a rule with the same name already exists
        fusion_engine.get_rule(rule.name)
        raise HTTPException(
            status_code=409, detail=f"Fusion rule already exists: {rule.name}"
        )
    except KeyError:
        # Register the new rule
        fusion_engine.register_rule(rule)
        return rule


@router.post("/fuse/{rule_name}", response_model=FusionResult)
async def fuse_data(
    rule_name: str,
    readings: Dict[SensorType, List[SensorReading]],
    request: Request,
    fusion_engine: FusionEngine = Depends(get_fusion_engine),
    storage: TimeSeriesStorage = Depends(get_storage),
) -> FusionResult:
    """Fuse sensor readings according to a rule.

    Args:
        rule_name: The name of the fusion rule to apply.
        readings: A dictionary mapping sensor types to lists of readings.
        request: The request object.
        fusion_engine: The fusion engine instance.
        storage: The storage instance.

    Returns:
        The result of the fusion operation.

    Raises:
        HTTPException: If the rule does not exist or the required sensor types are not provided.
    """
    try:
        # Apply the fusion rule
        result = fusion_engine.fuse(rule_name, readings)

        # Store the result
        await storage.store_fusion_result(result)

        return result
    except KeyError:
        raise HTTPException(status_code=404, detail=f"Fusion rule not found: {rule_name}")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
