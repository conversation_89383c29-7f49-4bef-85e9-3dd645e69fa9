"""Storage module for time-series data."""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union

import aiosqlite
import pandas as pd
from sqlalchemy import (
    Column,
    DateTime,
    Float,
    ForeignKey,
    Integer,
    String,
    Table,
    Text,
    create_engine,
    func,
    select,
)
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker

from enviromesh.core.models import (
    FusionResult,
    SensorReading,
    SensorType,
)

logger = logging.getLogger(__name__)

Base = declarative_base()


class SensorReadingDB(Base):
    """Database model for sensor readings."""

    __tablename__ = "sensor_readings"

    id = Column(Integer, primary_key=True)
    sensor_id = Column(String, nullable=False, index=True)
    sensor_type = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    value = Column(Float, nullable=False)
    unit = Column(String, nullable=False)
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    altitude = Column(Float, nullable=True)
    location_name = Column(String, nullable=True)
    metadata = Column(Text, nullable=True)  # JSON string


class FusionResultDB(Base):
    """Database model for fusion results."""

    __tablename__ = "fusion_results"

    id = Column(Integer, primary_key=True)
    rule_name = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    value = Column(Float, nullable=False)
    metadata = Column(Text, nullable=True)  # JSON string


class TimeSeriesStorage:
    """Storage for time-series data."""

    def __init__(self, db_url: str = "sqlite+aiosqlite:///enviromesh.db") -> None:
        """Initialize the storage.

        Args:
            db_url: The database URL.
        """
        self.engine = create_async_engine(db_url, echo=False)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )

    async def initialize(self) -> None:
        """Initialize the database."""
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("Database initialized")

    async def store_sensor_reading(self, reading: SensorReading) -> None:
        """Store a sensor reading.

        Args:
            reading: The sensor reading to store.
        """
        async with self.async_session() as session:
            async with session.begin():
                db_reading = SensorReadingDB(
                    sensor_id=reading.sensor_id,
                    sensor_type=reading.sensor_type.value,
                    timestamp=reading.timestamp,
                    value=reading.value,
                    unit=reading.unit,
                    latitude=reading.location.latitude if reading.location else None,
                    longitude=reading.location.longitude if reading.location else None,
                    altitude=reading.location.altitude if reading.location else None,
                    location_name=reading.location.name if reading.location else None,
                    metadata=str(reading.metadata) if reading.metadata else None,
                )
                session.add(db_reading)

    async def store_fusion_result(self, result: FusionResult) -> None:
        """Store a fusion result.

        Args:
            result: The fusion result to store.
        """
        async with self.async_session() as session:
            async with session.begin():
                db_result = FusionResultDB(
                    rule_name=result.rule_name,
                    timestamp=result.timestamp,
                    value=result.value,
                    metadata=str(result.metadata) if result.metadata else None,
                )
                session.add(db_result)

    async def get_sensor_readings(
        self,
        sensor_ids: Optional[List[str]] = None,
        sensor_types: Optional[List[SensorType]] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[SensorReading]:
        """Get sensor readings.

        Args:
            sensor_ids: Optional list of sensor IDs to filter by.
            sensor_types: Optional list of sensor types to filter by.
            start_time: Optional start time to filter by.
            end_time: Optional end time to filter by.
            limit: Maximum number of results to return.
            offset: Number of results to skip.

        Returns:
            A list of sensor readings.
        """
        async with self.async_session() as session:
            query = select(SensorReadingDB)

            if sensor_ids:
                query = query.filter(SensorReadingDB.sensor_id.in_(sensor_ids))

            if sensor_types:
                sensor_type_values = [st.value for st in sensor_types]
                query = query.filter(SensorReadingDB.sensor_type.in_(sensor_type_values))

            if start_time:
                query = query.filter(SensorReadingDB.timestamp >= start_time)

            if end_time:
                query = query.filter(SensorReadingDB.timestamp <= end_time)

            query = query.order_by(SensorReadingDB.timestamp.desc())
            query = query.limit(limit).offset(offset)

            result = await session.execute(query)
            db_readings = result.scalars().all()

            readings = []
            for db_reading in db_readings:
                from enviromesh.core.models import Location

                location = None
                if db_reading.latitude is not None and db_reading.longitude is not None:
                    location = Location(
                        latitude=db_reading.latitude,
                        longitude=db_reading.longitude,
                        altitude=db_reading.altitude,
                        name=db_reading.location_name,
                    )

                metadata = {}
                if db_reading.metadata:
                    try:
                        import ast
                        metadata = ast.literal_eval(db_reading.metadata)
                    except (SyntaxError, ValueError):
                        pass

                reading = SensorReading(
                    sensor_id=db_reading.sensor_id,
                    sensor_type=SensorType(db_reading.sensor_type),
                    timestamp=db_reading.timestamp,
                    value=db_reading.value,
                    unit=db_reading.unit,
                    location=location,
                    metadata=metadata,
                )
                readings.append(reading)

            return readings

    async def get_fusion_results(
        self,
        rule_names: Optional[List[str]] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[FusionResult]:
        """Get fusion results.

        Args:
            rule_names: Optional list of rule names to filter by.
            start_time: Optional start time to filter by.
            end_time: Optional end time to filter by.
            limit: Maximum number of results to return.
            offset: Number of results to skip.

        Returns:
            A list of fusion results.
        """
        async with self.async_session() as session:
            query = select(FusionResultDB)

            if rule_names:
                query = query.filter(FusionResultDB.rule_name.in_(rule_names))

            if start_time:
                query = query.filter(FusionResultDB.timestamp >= start_time)

            if end_time:
                query = query.filter(FusionResultDB.timestamp <= end_time)

            query = query.order_by(FusionResultDB.timestamp.desc())
            query = query.limit(limit).offset(offset)

            result = await session.execute(query)
            db_results = result.scalars().all()

            fusion_results = []
            for db_result in db_results:
                metadata = {}
                if db_result.metadata:
                    try:
                        import ast
                        metadata = ast.literal_eval(db_result.metadata)
                    except (SyntaxError, ValueError):
                        pass

                # We don't have the input readings here, so we'll return an empty list
                fusion_result = FusionResult(
                    rule_name=db_result.rule_name,
                    timestamp=db_result.timestamp,
                    value=db_result.value,
                    input_readings=[],
                    metadata=metadata,
                )
                fusion_results.append(fusion_result)

            return fusion_results
