import React, { useState } from 'react';
import { Cog6ToothIcon, ServerIcon, BellIcon, KeyIcon } from '@heroicons/react/24/outline';

const Settings: React.FC = () => {
  const [mqttSettings, setMqttSettings] = useState({
    broker: 'localhost',
    port: '1883',
    username: 'enviromesh',
    password: 'enviromesh',
    clientId: 'enviromesh-client',
  });

  const [apiSettings, setApiSettings] = useState({
    openaiApiKey: '',
    enableWebhooks: true,
    webhookUrl: 'https://example.com/webhook',
  });

  const [notificationSettings, setNotificationSettings] = useState({
    enableEmailNotifications: false,
    emailAddress: '',
    enableSlackNotifications: false,
    slackWebhook: '',
  });

  const handleMqttChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setMqttSettings(prev => ({ ...prev, [name]: value }));
  };

  const handleApiChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setApiSettings(prev => ({ 
      ...prev, 
      [name]: type === 'checkbox' ? checked : value 
    }));
  };

  const handleNotificationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setNotificationSettings(prev => ({ 
      ...prev, 
      [name]: type === 'checkbox' ? checked : value 
    }));
  };

  const handleMqttSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Save MQTT settings
    console.log('MQTT settings saved:', mqttSettings);
    // In a real app, you would send these to the backend
  };

  const handleApiSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Save API settings
    console.log('API settings saved:', apiSettings);
    // In a real app, you would send these to the backend
  };

  const handleNotificationSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Save notification settings
    console.log('Notification settings saved:', notificationSettings);
    // In a real app, you would send these to the backend
  };

  return (
    <div>
      <h1 className="text-2xl font-semibold text-gray-900">Settings</h1>
      
      {/* MQTT Settings */}
      <div className="mt-6 bg-white shadow sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <ServerIcon className="h-6 w-6 text-primary-500 mr-3" />
            <h3 className="text-lg leading-6 font-medium text-gray-900">MQTT Settings</h3>
          </div>
          <div className="mt-2 max-w-xl text-sm text-gray-500">
            <p>Configure the MQTT broker connection for sensor data ingestion.</p>
          </div>
          <form className="mt-5 space-y-4" onSubmit={handleMqttSubmit}>
            <div className="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-6">
              <div className="sm:col-span-3">
                <label htmlFor="broker" className="block text-sm font-medium text-gray-700">
                  Broker Address
                </label>
                <input
                  type="text"
                  name="broker"
                  id="broker"
                  value={mqttSettings.broker}
                  onChange={handleMqttChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>

              <div className="sm:col-span-3">
                <label htmlFor="port" className="block text-sm font-medium text-gray-700">
                  Port
                </label>
                <input
                  type="text"
                  name="port"
                  id="port"
                  value={mqttSettings.port}
                  onChange={handleMqttChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>

              <div className="sm:col-span-3">
                <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                  Username
                </label>
                <input
                  type="text"
                  name="username"
                  id="username"
                  value={mqttSettings.username}
                  onChange={handleMqttChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>

              <div className="sm:col-span-3">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <input
                  type="password"
                  name="password"
                  id="password"
                  value={mqttSettings.password}
                  onChange={handleMqttChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>

              <div className="sm:col-span-6">
                <label htmlFor="clientId" className="block text-sm font-medium text-gray-700">
                  Client ID
                </label>
                <input
                  type="text"
                  name="clientId"
                  id="clientId"
                  value={mqttSettings.clientId}
                  onChange={handleMqttChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Save MQTT Settings
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* API Settings */}
      <div className="mt-6 bg-white shadow sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <KeyIcon className="h-6 w-6 text-primary-500 mr-3" />
            <h3 className="text-lg leading-6 font-medium text-gray-900">API Settings</h3>
          </div>
          <div className="mt-2 max-w-xl text-sm text-gray-500">
            <p>Configure API keys and external integrations.</p>
          </div>
          <form className="mt-5 space-y-4" onSubmit={handleApiSubmit}>
            <div>
              <label htmlFor="openaiApiKey" className="block text-sm font-medium text-gray-700">
                OpenAI API Key
              </label>
              <input
                type="password"
                name="openaiApiKey"
                id="openaiApiKey"
                value={apiSettings.openaiApiKey}
                onChange={handleApiChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="sk-..."
              />
              <p className="mt-1 text-xs text-gray-500">Required for AI insights generation.</p>
            </div>

            <div className="flex items-start">
              <div className="flex items-center h-5">
                <input
                  id="enableWebhooks"
                  name="enableWebhooks"
                  type="checkbox"
                  checked={apiSettings.enableWebhooks}
                  onChange={handleApiChange}
                  className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="enableWebhooks" className="font-medium text-gray-700">
                  Enable Webhooks
                </label>
                <p className="text-gray-500">Send data to external systems via webhooks.</p>
              </div>
            </div>

            {apiSettings.enableWebhooks && (
              <div>
                <label htmlFor="webhookUrl" className="block text-sm font-medium text-gray-700">
                  Webhook URL
                </label>
                <input
                  type="text"
                  name="webhookUrl"
                  id="webhookUrl"
                  value={apiSettings.webhookUrl}
                  onChange={handleApiChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>
            )}

            <div className="flex justify-end">
              <button
                type="submit"
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Save API Settings
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Notification Settings */}
      <div className="mt-6 bg-white shadow sm:rounded-lg mb-12">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <BellIcon className="h-6 w-6 text-primary-500 mr-3" />
            <h3 className="text-lg leading-6 font-medium text-gray-900">Notification Settings</h3>
          </div>
          <div className="mt-2 max-w-xl text-sm text-gray-500">
            <p>Configure how you want to receive notifications.</p>
          </div>
          <form className="mt-5 space-y-4" onSubmit={handleNotificationSubmit}>
            <div className="flex items-start">
              <div className="flex items-center h-5">
                <input
                  id="enableEmailNotifications"
                  name="enableEmailNotifications"
                  type="checkbox"
                  checked={notificationSettings.enableEmailNotifications}
                  onChange={handleNotificationChange}
                  className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="enableEmailNotifications" className="font-medium text-gray-700">
                  Email Notifications
                </label>
                <p className="text-gray-500">Receive alerts and reports via email.</p>
              </div>
            </div>

            {notificationSettings.enableEmailNotifications && (
              <div>
                <label htmlFor="emailAddress" className="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <input
                  type="email"
                  name="emailAddress"
                  id="emailAddress"
                  value={notificationSettings.emailAddress}
                  onChange={handleNotificationChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>
            )}

            <div className="flex items-start">
              <div className="flex items-center h-5">
                <input
                  id="enableSlackNotifications"
                  name="enableSlackNotifications"
                  type="checkbox"
                  checked={notificationSettings.enableSlackNotifications}
                  onChange={handleNotificationChange}
                  className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="enableSlackNotifications" className="font-medium text-gray-700">
                  Slack Notifications
                </label>
                <p className="text-gray-500">Receive alerts and reports via Slack.</p>
              </div>
            </div>

            {notificationSettings.enableSlackNotifications && (
              <div>
                <label htmlFor="slackWebhook" className="block text-sm font-medium text-gray-700">
                  Slack Webhook URL
                </label>
                <input
                  type="text"
                  name="slackWebhook"
                  id="slackWebhook"
                  value={notificationSettings.slackWebhook}
                  onChange={handleNotificationChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                />
              </div>
            )}

            <div className="flex justify-end">
              <button
                type="submit"
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Save Notification Settings
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Settings;
