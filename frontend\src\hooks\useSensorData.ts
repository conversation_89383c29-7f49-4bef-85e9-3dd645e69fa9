import { useState, useEffect } from 'react';
import axios from 'axios';

interface SensorData {
  sensor_id: string;
  sensor_type: string;
  timestamp: string;
  value: number;
  unit: string;
  location?: {
    latitude: number;
    longitude: number;
    altitude?: number;
    name?: string;
  };
  metadata?: Record<string, any>;
}

interface UseSensorDataResult {
  sensorData: SensorData[] | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
}

export const useSensorData = (): UseSensorDataResult => {
  const [sensorData, setSensorData] = useState<SensorData[] | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [refetchIndex, setRefetchIndex] = useState<number>(0);

  const refetch = () => {
    setRefetchIndex(prev => prev + 1);
  };

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const response = await axios.get('/api/mesh');
        setSensorData(response.data.sensor_readings);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('An unknown error occurred'));
        setSensorData(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [refetchIndex]);

  return { sensorData, isLoading, error, refetch };
};
