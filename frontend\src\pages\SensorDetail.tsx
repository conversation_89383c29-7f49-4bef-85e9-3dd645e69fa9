import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import { 
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer 
} from 'recharts';
import { format } from 'date-fns';

interface SensorData {
  sensor_id: string;
  sensor_type: string;
  timestamp: string;
  value: number;
  unit: string;
  location?: {
    latitude: number;
    longitude: number;
    altitude?: number;
    name?: string;
  };
  metadata?: Record<string, any>;
}

const SensorDetail: React.FC = () => {
  const { sensorId } = useParams<{ sensorId: string }>();
  const [sensor, setSensor] = useState<SensorData | null>(null);
  const [historicalData, setHistoricalData] = useState<SensorData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSensorData = async () => {
      setIsLoading(true);
      try {
        // Fetch the latest sensor reading
        const response = await axios.get(`/api/mesh?sensor_ids=${sensorId}&limit=1`);
        if (response.data.sensor_readings.length > 0) {
          setSensor(response.data.sensor_readings[0]);
        }

        // Fetch historical data
        const historicalResponse = await axios.get(`/api/batch?sensor_ids=${sensorId}&limit=100`);
        setHistoricalData(historicalResponse.data.sensor_readings);
        
        setError(null);
      } catch (err) {
        setError('Failed to fetch sensor data');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    if (sensorId) {
      fetchSensorData();
    }
  }, [sensorId]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  if (!sensor) {
    return <div>No sensor found with ID: {sensorId}</div>;
  }

  // Format the sensor type for display
  const formatSensorType = (type: string) => {
    return type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  return (
    <div>
      <h1 className="text-2xl font-semibold text-gray-900">
        {formatSensorType(sensor.sensor_type)} Sensor: {sensor.sensor_id}
      </h1>

      {/* Sensor Details */}
      <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Sensor Details</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">Details and latest reading.</p>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Sensor ID</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{sensor.sensor_id}</dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Type</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{formatSensorType(sensor.sensor_type)}</dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Latest Value</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {sensor.value} {sensor.unit}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {format(new Date(sensor.timestamp), 'PPpp')}
              </dd>
            </div>
            {sensor.location && (
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Location</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {sensor.location.name || 'Unnamed Location'}<br />
                  <span className="text-xs text-gray-500">
                    Lat: {sensor.location.latitude}, Lng: {sensor.location.longitude}
                    {sensor.location.altitude && `, Alt: ${sensor.location.altitude}m`}
                  </span>
                </dd>
              </div>
            )}
          </dl>
        </div>
      </div>

      {/* Historical Data Chart */}
      <div className="mt-8">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900">Historical Data</h2>
          <div className="mt-4 h-96">
            {historicalData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={historicalData.map(d => ({
                    ...d,
                    timestamp: new Date(d.timestamp)
                  }))}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tickFormatter={(timestamp) => format(timestamp, 'HH:mm:ss')}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(timestamp) => format(timestamp, 'yyyy-MM-dd HH:mm:ss')}
                  />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    stroke="#8884d8" 
                    activeDot={{ r: 8 }} 
                    name={`${formatSensorType(sensor.sensor_type)} (${sensor.unit})`}
                  />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-full">
                <p className="text-gray-500">No historical data available</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Metadata */}
      {sensor.metadata && Object.keys(sensor.metadata).length > 0 && (
        <div className="mt-8 mb-12">
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Metadata</h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">Additional sensor information.</p>
            </div>
            <div className="border-t border-gray-200">
              <dl>
                {Object.entries(sensor.metadata).map(([key, value], index) => (
                  <div key={key} className={`${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'} px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6`}>
                    <dt className="text-sm font-medium text-gray-500">{key}</dt>
                    <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                      {typeof value === 'object' 
                        ? JSON.stringify(value, null, 2) 
                        : String(value)
                      }
                    </dd>
                  </div>
                ))}
              </dl>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SensorDetail;
