"""HTTP client for ingesting sensor data from REST APIs."""

import asyncio
import logging
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Union

import httpx
from pydantic import ValidationError

from enviromesh.core.models import SensorReading, SensorType
from enviromesh.core.storage import TimeSeriesStorage

logger = logging.getLogger(__name__)


class HTTPClient:
    """HTTP client for ingesting sensor data from REST APIs."""

    def __init__(
        self,
        storage: Optional[TimeSeriesStorage] = None,
    ) -> None:
        """Initialize the HTTP client.

        Args:
            storage: Optional storage for persisting readings.
        """
        self.storage = storage
        self.client = httpx.AsyncClient(timeout=30.0)
        self.polling_tasks: Dict[str, asyncio.Task] = {}
        self.callbacks: Dict[str, List[Callable[[SensorReading], None]]] = {}

    async def close(self) -> None:
        """Close the HTTP client."""
        # Cancel all polling tasks
        for task in self.polling_tasks.values():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

        # Close the HTTP client
        await self.client.aclose()
        logger.info("HTTP client closed")

    def register_callback(
        self, endpoint_id: str, callback: Callable[[SensorReading], None]
    ) -> None:
        """Register a callback for an endpoint.

        Args:
            endpoint_id: The ID of the endpoint to register the callback for.
            callback: The callback function to call when data is received.
        """
        if endpoint_id not in self.callbacks:
            self.callbacks[endpoint_id] = []
        self.callbacks[endpoint_id].append(callback)
        logger.info(f"Registered callback for endpoint: {endpoint_id}")

    async def fetch_once(
        self,
        url: str,
        endpoint_id: str,
        method: str = "GET",
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, str]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        sensor_type: Optional[SensorType] = None,
        sensor_id: Optional[str] = None,
        transform_func: Optional[Callable[[Dict[str, Any]], Dict[str, Any]]] = None,
    ) -> List[SensorReading]:
        """Fetch data from an HTTP endpoint once.

        Args:
            url: The URL to fetch data from.
            endpoint_id: An ID for the endpoint.
            method: The HTTP method to use.
            headers: Optional headers to include in the request.
            params: Optional query parameters to include in the request.
            json_data: Optional JSON data to include in the request body.
            sensor_type: Optional sensor type to use if not specified in the response.
            sensor_id: Optional sensor ID to use if not specified in the response.
            transform_func: Optional function to transform the response data.

        Returns:
            A list of sensor readings.

        Raises:
            httpx.HTTPError: If the request fails.
        """
        try:
            response = await self.client.request(
                method,
                url,
                headers=headers,
                params=params,
                json=json_data,
            )
            response.raise_for_status()

            # Parse the response
            data = response.json()

            # Apply transformation if provided
            if transform_func:
                data = transform_func(data)

            # Convert to a list if it's not already
            if not isinstance(data, list):
                data = [data]

            readings = []
            for item in data:
                # Add sensor_type and sensor_id if provided and not in the data
                if sensor_type and "sensor_type" not in item:
                    item["sensor_type"] = sensor_type.value
                if sensor_id and "sensor_id" not in item:
                    item["sensor_id"] = sensor_id

                try:
                    reading = SensorReading(**item)
                    readings.append(reading)

                    # Store the reading if storage is available
                    if self.storage:
                        await self.storage.store_sensor_reading(reading)

                    # Call registered callbacks
                    if endpoint_id in self.callbacks:
                        for callback in self.callbacks[endpoint_id]:
                            callback(reading)

                except ValidationError as e:
                    logger.error(f"Failed to validate sensor reading: {e}")

            return readings

        except httpx.HTTPError as e:
            logger.error(f"HTTP request failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Error fetching data from {url}: {e}")
            raise

    async def poll_endpoint(
        self,
        url: str,
        endpoint_id: str,
        interval_seconds: int = 60,
        method: str = "GET",
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, str]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        sensor_type: Optional[SensorType] = None,
        sensor_id: Optional[str] = None,
        transform_func: Optional[Callable[[Dict[str, Any]], Dict[str, Any]]] = None,
    ) -> None:
        """Poll an HTTP endpoint at regular intervals.

        Args:
            url: The URL to poll.
            endpoint_id: An ID for the endpoint.
            interval_seconds: The interval between polls in seconds.
            method: The HTTP method to use.
            headers: Optional headers to include in the request.
            params: Optional query parameters to include in the request.
            json_data: Optional JSON data to include in the request body.
            sensor_type: Optional sensor type to use if not specified in the response.
            sensor_id: Optional sensor ID to use if not specified in the response.
            transform_func: Optional function to transform the response data.
        """
        logger.info(f"Starting to poll endpoint {endpoint_id} at {url}")
        while True:
            try:
                await self.fetch_once(
                    url,
                    endpoint_id,
                    method,
                    headers,
                    params,
                    json_data,
                    sensor_type,
                    sensor_id,
                    transform_func,
                )
            except Exception as e:
                logger.error(f"Error polling endpoint {endpoint_id}: {e}")

            # Wait for the next interval
            await asyncio.sleep(interval_seconds)

    def start_polling(
        self,
        url: str,
        endpoint_id: str,
        interval_seconds: int = 60,
        method: str = "GET",
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, str]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        sensor_type: Optional[SensorType] = None,
        sensor_id: Optional[str] = None,
        transform_func: Optional[Callable[[Dict[str, Any]], Dict[str, Any]]] = None,
    ) -> None:
        """Start polling an HTTP endpoint at regular intervals.

        Args:
            url: The URL to poll.
            endpoint_id: An ID for the endpoint.
            interval_seconds: The interval between polls in seconds.
            method: The HTTP method to use.
            headers: Optional headers to include in the request.
            params: Optional query parameters to include in the request.
            json_data: Optional JSON data to include in the request body.
            sensor_type: Optional sensor type to use if not specified in the response.
            sensor_id: Optional sensor ID to use if not specified in the response.
            transform_func: Optional function to transform the response data.
        """
        # Cancel any existing polling task for this endpoint
        if endpoint_id in self.polling_tasks:
            self.polling_tasks[endpoint_id].cancel()

        # Start a new polling task
        task = asyncio.create_task(
            self.poll_endpoint(
                url,
                endpoint_id,
                interval_seconds,
                method,
                headers,
                params,
                json_data,
                sensor_type,
                sensor_id,
                transform_func,
            )
        )
        self.polling_tasks[endpoint_id] = task
        logger.info(f"Started polling task for endpoint {endpoint_id}")

    def stop_polling(self, endpoint_id: str) -> None:
        """Stop polling an HTTP endpoint.

        Args:
            endpoint_id: The ID of the endpoint to stop polling.
        """
        if endpoint_id in self.polling_tasks:
            self.polling_tasks[endpoint_id].cancel()
            del self.polling_tasks[endpoint_id]
            logger.info(f"Stopped polling task for endpoint {endpoint_id}")
        else:
            logger.warning(f"No polling task found for endpoint {endpoint_id}")
