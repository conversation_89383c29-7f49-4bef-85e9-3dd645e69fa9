[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "enviromesh"
version = "0.1.0"
description = "EnviroMesh - Adaptive network for environmental data fusion in real-time"
authors = [
    {name = "EnviroMesh Team"}
]
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Science/Research",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "multiphenicsx",
    "fastapi>=0.95.0",
    "uvicorn>=0.21.0",
    "paho-mqtt>=2.0.0",
    "numpy>=1.21.0",
    "pandas>=1.5.0",
    "websockets>=11.0.0",
    "httpx>=0.24.0",
    "pydantic>=2.0.0",
    "openai>=1.0.0",
    "python-dotenv>=1.0.0",
    "sqlalchemy>=2.0.0",
    "aiosqlite>=0.19.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "ruff>=0.0.270",
]

[tool.setuptools]
packages = ["enviromesh"]

[tool.black]
line-length = 88
target-version = ["py39"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.ruff]
line-length = 88
target-version = "py39"
select = ["E", "F", "I"]
