import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup, useMap } from 'react-leaflet';
import L from 'leaflet';
import { Link } from 'react-router-dom';
import { format } from 'date-fns';

// Fix for Leaflet marker icons
import icon from 'leaflet/dist/images/marker-icon.png';
import iconShadow from 'leaflet/dist/images/marker-shadow.png';

let DefaultIcon = L.icon({
  iconUrl: icon,
  shadowUrl: iconShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
});

L.Marker.prototype.options.icon = DefaultIcon;

// Custom marker icons for different sensor types
const sensorIcons = {
  air_quality: L.icon({
    iconUrl: '/markers/air-quality.png',
    iconSize: [32, 32],
    iconAnchor: [16, 32],
    popupAnchor: [0, -32],
  }),
  noise: L.icon({
    iconUrl: '/markers/noise.png',
    iconSize: [32, 32],
    iconAnchor: [16, 32],
    popupAnchor: [0, -32],
  }),
  traffic: L.icon({
    iconUrl: '/markers/traffic.png',
    iconSize: [32, 32],
    iconAnchor: [16, 32],
    popupAnchor: [0, -32],
  }),
  light: L.icon({
    iconUrl: '/markers/light.png',
    iconSize: [32, 32],
    iconAnchor: [16, 32],
    popupAnchor: [0, -32],
  }),
  default: DefaultIcon,
};

// Component to recenter the map when sensors change
const MapUpdater = ({ sensors }: { sensors: any[] }) => {
  const map = useMap();
  
  useEffect(() => {
    if (sensors.length > 0) {
      // Create bounds from all sensor locations
      const bounds = L.latLngBounds(
        sensors
          .filter(sensor => sensor.location)
          .map(sensor => [sensor.location.latitude, sensor.location.longitude])
      );
      
      // Only fit bounds if we have valid locations
      if (bounds.isValid()) {
        map.fitBounds(bounds, { padding: [50, 50] });
      }
    }
  }, [sensors, map]);
  
  return null;
};

interface SensorMapProps {
  sensors: any[];
  height?: string;
  width?: string;
  className?: string;
}

const SensorMap: React.FC<SensorMapProps> = ({ 
  sensors, 
  height = '500px', 
  width = '100%',
  className = '' 
}) => {
  // Default center (New York City)
  const defaultCenter: [number, number] = [40.7128, -74.0060];
  
  // Get icon based on sensor type
  const getIcon = (sensorType: string) => {
    return sensorIcons[sensorType as keyof typeof sensorIcons] || sensorIcons.default;
  };
  
  // Format the sensor type for display
  const formatSensorType = (type: string) => {
    return type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  return (
    <div style={{ height, width }} className={`rounded-lg overflow-hidden shadow-md ${className}`}>
      <MapContainer 
        center={defaultCenter} 
        zoom={13} 
        style={{ height: '100%', width: '100%' }}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        {sensors.filter(sensor => sensor.location).map((sensor, index) => (
          <Marker 
            key={`${sensor.sensor_id}-${index}`}
            position={[sensor.location.latitude, sensor.location.longitude]}
            icon={getIcon(sensor.sensor_type)}
          >
            <Popup>
              <div className="p-1">
                <h3 className="font-medium text-gray-900">{formatSensorType(sensor.sensor_type)} Sensor</h3>
                <p className="text-sm text-gray-600">ID: {sensor.sensor_id}</p>
                <p className="text-sm text-gray-600">
                  Value: {sensor.value} {sensor.unit}
                </p>
                {sensor.timestamp && (
                  <p className="text-xs text-gray-500 mt-1">
                    {format(new Date(sensor.timestamp), 'MMM d, yyyy HH:mm:ss')}
                  </p>
                )}
                <div className="mt-2">
                  <Link
                    to={`/sensors/${sensor.sensor_id}`}
                    className="text-xs font-medium text-primary-600 hover:text-primary-500"
                  >
                    View details
                  </Link>
                </div>
              </div>
            </Popup>
          </Marker>
        ))}
        
        <MapUpdater sensors={sensors} />
      </MapContainer>
    </div>
  );
};

export default SensorMap;
