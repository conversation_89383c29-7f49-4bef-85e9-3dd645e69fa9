"""API routes for batch data operations."""

from datetime import datetime, timed<PERSON>ta
from typing import Dict, <PERSON>, Optional, Union

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from pydantic import BaseModel

from enviromesh.core.models import (
    FusionResult,
    MeshQuery,
    SensorReading,
    SensorType,
)
from enviromesh.core.storage import TimeSeriesStorage

router = APIRouter()


def get_storage(request: Request) -> TimeSeriesStorage:
    """Get the storage from the request state.

    Args:
        request: The request object.

    Returns:
        The storage instance.
    """
    return request.app.state.storage


class BatchResponse(BaseModel):
    """Response model for batch data."""

    sensor_readings: List[SensorReading]
    fusion_results: List[FusionResult]
    metadata: Dict[str, Union[int, str, float]]


@router.get("/", response_model=BatchResponse)
async def get_batch_data(
    request: Request,
    node_ids: Optional[List[str]] = Query(None),
    sensor_types: Optional[List[SensorType]] = Query(None),
    fusion_rules: Optional[List[str]] = Query(None),
    start_time: Optional[datetime] = Query(None),
    end_time: Optional[datetime] = Query(None),
    limit: int = Query(1000, ge=1, le=10000),
    offset: int = Query(0, ge=0),
    storage: TimeSeriesStorage = Depends(get_storage),
) -> BatchResponse:
    """Get batch data for historical analysis.

    Args:
        request: The request object.
        node_ids: Optional list of node IDs to filter by.
        sensor_types: Optional list of sensor types to filter by.
        fusion_rules: Optional list of fusion rules to filter by.
        start_time: Optional start time to filter by.
        end_time: Optional end time to filter by.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        storage: The storage instance.

    Returns:
        A BatchResponse object containing sensor readings and fusion results.
    """
    # Set default time range if not provided
    if not start_time:
        start_time = datetime.utcnow() - timedelta(days=7)
    if not end_time:
        end_time = datetime.utcnow()

    # Get sensor readings
    sensor_readings = await storage.get_sensor_readings(
        sensor_ids=node_ids,
        sensor_types=sensor_types,
        start_time=start_time,
        end_time=end_time,
        limit=limit,
        offset=offset,
    )

    # Get fusion results
    fusion_results = await storage.get_fusion_results(
        rule_names=fusion_rules,
        start_time=start_time,
        end_time=end_time,
        limit=limit,
        offset=offset,
    )

    # Calculate metadata
    metadata = {
        "total_sensor_readings": len(sensor_readings),
        "total_fusion_results": len(fusion_results),
        "start_time": start_time.isoformat() if start_time else None,
        "end_time": end_time.isoformat() if end_time else None,
        "limit": limit,
        "offset": offset,
    }

    return BatchResponse(
        sensor_readings=sensor_readings,
        fusion_results=fusion_results,
        metadata=metadata,
    )


@router.post("/query", response_model=BatchResponse)
async def query_batch_data(
    query: MeshQuery,
    request: Request,
    storage: TimeSeriesStorage = Depends(get_storage),
) -> BatchResponse:
    """Query batch data using a MeshQuery object.

    Args:
        query: The query parameters.
        request: The request object.
        storage: The storage instance.

    Returns:
        A BatchResponse object containing sensor readings and fusion results.
    """
    # Get sensor readings
    sensor_readings = await storage.get_sensor_readings(
        sensor_ids=query.node_ids,
        sensor_types=query.sensor_types,
        start_time=query.start_time,
        end_time=query.end_time,
        limit=query.limit,
        offset=query.offset,
    )

    # Get fusion results
    fusion_results = await storage.get_fusion_results(
        rule_names=query.fusion_rules,
        start_time=query.start_time,
        end_time=query.end_time,
        limit=query.limit,
        offset=query.offset,
    )

    # Calculate metadata
    metadata = {
        "total_sensor_readings": len(sensor_readings),
        "total_fusion_results": len(fusion_results),
        "start_time": query.start_time.isoformat() if query.start_time else None,
        "end_time": query.end_time.isoformat() if query.end_time else None,
        "limit": query.limit,
        "offset": query.offset,
    }

    return BatchResponse(
        sensor_readings=sensor_readings,
        fusion_results=fusion_results,
        metadata=metadata,
    )
