{"name": "enviromesh-frontend", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "axios": "^1.6.2", "date-fns": "^2.30.0", "leaflet": "^1.9.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-leaflet": "^4.2.1", "react-router-dom": "^6.20.0", "recharts": "^2.10.1", "swr": "^2.2.4"}, "devDependencies": {"@types/leaflet": "^1.9.8", "@types/node": "^20.10.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.3.2", "vite": "^5.0.2"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}}