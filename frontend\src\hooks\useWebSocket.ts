import { useEffect, useRef, useState } from 'react';

interface WebSocketHook {
  lastMessage: WebSocketEventMap['message'] | null;
  readyState: number;
  sendMessage: (message: string) => void;
}

export const useWebSocket = (url: string): WebSocketHook => {
  const [lastMessage, setLastMessage] = useState<WebSocketEventMap['message'] | null>(null);
  const [readyState, setReadyState] = useState<number>(WebSocket.CONNECTING);
  const webSocketRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Create WebSocket connection
    const webSocket = new WebSocket(url);
    webSocketRef.current = webSocket;

    // Connection opened
    webSocket.addEventListener('open', () => {
      setReadyState(WebSocket.OPEN);
      console.log('WebSocket connection established');
    });

    // Listen for messages
    webSocket.addEventListener('message', (event) => {
      setLastMessage(event);
    });

    // Connection closed
    webSocket.addEventListener('close', () => {
      setReadyState(WebSocket.CLOSED);
      console.log('WebSocket connection closed');
    });

    // Connection error
    webSocket.addEventListener('error', (error) => {
      console.error('WebSocket error:', error);
    });

    // Clean up on unmount
    return () => {
      webSocket.close();
    };
  }, [url]);

  // Function to send messages
  const sendMessage = (message: string) => {
    if (webSocketRef.current && webSocketRef.current.readyState === WebSocket.OPEN) {
      webSocketRef.current.send(message);
    } else {
      console.error('WebSocket is not connected');
    }
  };

  return { lastMessage, readyState, sendMessage };
};
