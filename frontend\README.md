# EnviroMesh Frontend

This is the frontend application for EnviroMesh, built with React and Tailwind CSS.

## Features

- Real-time dashboard for environmental data visualization
- WebSocket connection for live updates
- Interactive charts and maps
- AI-powered insights

## Getting Started

### Prerequisites

- Node.js 16+
- npm or yarn

### Installation

```bash
# Install dependencies
npm install
# or
yarn install
```

### Development

```bash
# Start the development server
npm run dev
# or
yarn dev
```

### Building for Production

```bash
# Build the application
npm run build
# or
yarn build
```

## Project Structure

```
frontend/
├── public/           # Static assets
├── src/
│   ├── components/   # React components
│   ├── hooks/        # Custom React hooks
│   ├── pages/        # Page components
│   ├── services/     # API services
│   ├── utils/        # Utility functions
│   ├── App.jsx       # Main application component
│   └── index.jsx     # Entry point
├── .env              # Environment variables
└── package.json      # Project configuration
```

## Technologies Used

- React
- Tailwind CSS
- Recharts for data visualization
- WebSocket for real-time updates
