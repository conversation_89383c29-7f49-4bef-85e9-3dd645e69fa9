"""Data models for EnviroMesh."""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class SensorType(str, Enum):
    """Types of sensors supported by EnviroMesh."""

    AIR_QUALITY = "air_quality"
    NOISE = "noise"
    TRAFFIC = "traffic"
    LIGHT = "light"
    TEMPERATURE = "temperature"
    HUMIDITY = "humidity"
    PRESSURE = "pressure"
    CUSTOM = "custom"


class Location(BaseModel):
    """Geographic location of a sensor."""

    latitude: float
    longitude: float
    altitude: Optional[float] = None
    name: Optional[str] = None


class SensorReading(BaseModel):
    """A single reading from a sensor."""

    sensor_id: str
    sensor_type: SensorType
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    value: float
    unit: str
    location: Optional[Location] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class SensorBatch(BaseModel):
    """A batch of readings from a sensor."""

    sensor_id: str
    sensor_type: SensorType
    readings: List[SensorReading]
    metadata: Dict[str, Any] = Field(default_factory=dict)


class FusionOperator(str, Enum):
    """Types of fusion operators supported by EnviroMesh."""

    MULTIPLY = "multiply"
    DIVIDE = "divide"
    ADD = "add"
    SUBTRACT = "subtract"
    POWER = "power"
    WEIGHTED_AVERAGE = "weighted_average"
    CUSTOM = "custom"


class FusionRule(BaseModel):
    """A rule for fusing multiple sensor readings."""

    name: str
    description: Optional[str] = None
    operator: FusionOperator
    inputs: List[SensorType]
    weights: Optional[List[float]] = None
    parameters: Dict[str, Any] = Field(default_factory=dict)


class FusionResult(BaseModel):
    """Result of applying a fusion rule to sensor readings."""

    rule_name: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    value: float
    input_readings: List[SensorReading]
    metadata: Dict[str, Any] = Field(default_factory=dict)


class MeshNode(BaseModel):
    """A node in the EnviroMesh network."""

    node_id: str
    name: str
    location: Location
    sensors: List[str]  # List of sensor_ids
    fusion_rules: List[str]  # List of rule names
    metadata: Dict[str, Any] = Field(default_factory=dict)


class MeshQuery(BaseModel):
    """A query for data from the EnviroMesh network."""

    node_ids: Optional[List[str]] = None
    sensor_types: Optional[List[SensorType]] = None
    fusion_rules: Optional[List[str]] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = 100
    offset: int = 0


class InsightRequest(BaseModel):
    """A request for an AI-generated insight about mesh data."""

    mesh_id: str
    time_range: Optional[str] = None
    focus: Optional[List[SensorType]] = None
    question: Optional[str] = None


class Insight(BaseModel):
    """An AI-generated insight about mesh data."""

    mesh_id: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    text: str
    data_points: List[Union[SensorReading, FusionResult]]
    confidence: float
    metadata: Dict[str, Any] = Field(default_factory=dict)
