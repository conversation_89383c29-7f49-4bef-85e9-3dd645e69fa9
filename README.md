<div align="center">
  <img src="logo.png" alt="EnviroMesh Logo" width="300">
  <h1>EnviroMesh</h1>
  <p><strong>Rede adaptativa para fusão de dados ambientais em tempo real</strong></p>
  
  [![License: MIT](https://img.shields.io/badge/License-MIT-blue.svg)](https://opensource.org/licenses/MIT)
  [![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
  [![FastAPI](https://img.shields.io/badge/FastAPI-005571?style=flat&logo=fastapi)](https://fastapi.tiangolo.com/)
  [![React](https://img.shields.io/badge/React-20232A?style=flat&logo=react&logoColor=61DAFB)](https://reactjs.org/)
</div>

## 📋 Sobre

**EnviroMesh** oferece uma plataforma unificada para agregar, filtrar e correlacionar dados heterogêneos de sensores de diversas fontes, como qualidade do ar, ruído, tráfego e sensores de luz.

## ✨ Recursos Principais

- **🔌 Ingestão Plug-and-play**: Conecte streams MQTT/HTTP de múltiplos tipos de sensores com um único cliente Python
- **🔄 Fusão de Fenômenos**: Aplique operadores para gerar métricas compostas (ex: "conforto urbano" = qualidade do ar × ruído⁻¹ × tráfego⁻⁰·⁵)
- **🚀 API Leve**: Endpoints FastAPI para consultas em tempo real e agregados históricos
- **📊 Dashboard em Tempo Real**: Interface React + Tailwind com gráficos animados baseados em WebSocket
- **🧠 Insights com IA**: Integração com GPT-4o mini para interpretação em linguagem natural de padrões de dados

## 🚀 Início Rápido

### Instalação

```bash
# Clone o repositório
git clone https://github.com/yourusername/enviromesh.git
cd enviromesh

# Instale as dependências
pip install -e .
```

### Executando a Aplicação

```bash
# Inicie o servidor API
uvicorn enviromesh.api.main:app --reload

# Em um terminal separado, inicie o frontend
cd frontend
npm install
npm run dev
```

## 🏗️ Arquitetura

EnviroMesh é construído com uma arquitetura modular:

<div align="center">
  <table>
    <tr>
      <th>Camada</th>
      <th>Componentes</th>
      <th>Função</th>
    </tr>
    <tr>
      <td>Ingestão de Dados</td>
      <td>Clientes MQTT e HTTP</td>
      <td>Coleta de dados de sensores</td>
    </tr>
    <tr>
      <td>Núcleo de Fusão</td>
      <td>Baseado em multiphenicsx</td>
      <td>Operações avançadas de fusão de dados</td>
    </tr>
    <tr>
      <td>Camada de API</td>
      <td>FastAPI</td>
      <td>Exposição de dados em tempo real e históricos</td>
    </tr>
    <tr>
      <td>Camada de Armazenamento</td>
      <td>Banco de dados de séries temporais</td>
      <td>Dados históricos</td>
    </tr>
    <tr>
      <td>Frontend</td>
      <td>React + Tailwind CSS</td>
      <td>Visualização</td>
    </tr>
  </table>
</div>

## 💻 Desenvolvimento

### Estrutura do Projeto

```
enviromesh/
├── enviromesh/
│   ├── api/            # Aplicação FastAPI
│   ├── core/           # Operadores de fusão e lógica principal
│   ├── ingestion/      # Clientes MQTT e HTTP
│   └── storage/        # Modelos de banco de dados e operações
├── frontend/           # Aplicação React + Tailwind CSS
├── tests/              # Suite de testes
└── pyproject.toml      # Configuração do projeto
```

### Executando Testes

```bash
pytest
```

## 📝 Licença

MIT
