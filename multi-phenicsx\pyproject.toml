[build-system]
requires = [
    "fenics-dolfinx >=0.10.0.dev0, <0.11.0",
    "mpi4py",
    "nanobind >= 2.1.0",
    "petsc4py",
    "scikit-build-core[pyproject]"
]
build-backend = "scikit_build_core.build"

[project]
name = "multiphenicsx"
version = "0.3.dev1"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
maintainers = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
description = "Easy prototyping of multiphysics problems on conforming meshes in FEniCSx"
license = {file = "COPYING"}
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: GNU Lesser General Public License v3 or later (LGPLv3+)",
    "Operating System :: POSIX",
    "Operating System :: POSIX :: Linux",
    "Operating System :: MacOS :: MacOS X",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Software Development :: Libraries :: Python Modules"
]
dependencies = [
    "fenics-dolfinx",
    "mpi4py",
    "numpy >= 1.21.0",
    "petsc4py"
]

[project.urls]
homepage = "https://multiphenics.github.io"
repository = "https://github.com/multiphenics/multiphenicsx"
issues = "https://github.com/multiphenics/multiphenicsx/issues"
funding = "https://github.com/sponsors/francesco-ballarin"

[project.optional-dependencies]
docs = [
    "sphinx"
]
lint = [
    "clang-format",
    "cmakelang",
    "isort",
    "mypy",
    "nbqa",
    "ruff",
    "yamllint"
]
tests = [
    "coverage[toml]",
    "nbvalx[unit-tests]",
    "pytest",
    "scipy"
]
tutorials = [
    "gmsh",
    "nbvalx[notebooks]",
    "scipy",
    "slepc4py",
    "sympy",
    "viskex @ git+https://github.com/viskex/viskex.git"
]

[tool.isort]
line_length = 120
multi_line_output = 4
order_by_type = false

[tool.mypy]
check_untyped_defs = true
disallow_any_unimported = true
disallow_untyped_defs = true
implicit_reexport = true
no_implicit_optional = true
pretty = true
show_error_codes = true
strict = true
warn_return_any = true
warn_unused_ignores = true

[[tool.mypy.overrides]]
module = [
    "dolfinx.cpp",
    "gmsh",
    "ipyparallel",
    "matplotlib",
    "matplotlib.*",
    "mpl_toolkits.*",
    "petsc4py",
    "petsc4py.PETSc",
    "plotly",
    "plotly.*",
    "scipy",
    "scipy.*",
    "slepc4py",
    "slepc4py.SLEPc",
    "sympy",
    "ufl"
]
ignore_missing_imports = true

[tool.pytest.ini_options]

[tool.ruff]
line-length = 120

[tool.ruff.lint]
select = ["ANN", "D", "E", "F", "FLY", "ICN", "N", "NPY", "Q", "RUF", "UP", "W"]
ignore = []
exclude = ["*.ipynb"]

[tool.ruff.lint.per-file-ignores]
"multiphenicsx/**/__init__.py" = ["F401"]
"multiphenicsx/fem/petsc.py" = ["N801", "N802", "N803", "N806"]
"tests/unit/fem/*.py" = ["N802", "N803", "N806"]
"tutorials/**/tutorial_*.py" = ["D100", "E741", "N802", "N803", "N806", "N816"]

[tool.ruff.lint.pycodestyle]
max-doc-length = 120

[tool.ruff.lint.pydocstyle]
convention = "numpy"

[tool.scikit-build]
cmake.source-dir = "multiphenicsx/cpp"
wheel.packages = ["multiphenicsx"]
sdist.exclude = ["*.cpp", "*.h", "CMakeLists.txt"]
