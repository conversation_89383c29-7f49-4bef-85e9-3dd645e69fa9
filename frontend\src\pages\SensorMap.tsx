import React, { useState, useEffect } from 'react';
import axios from 'axios';
import SensorMapComponent from '../components/SensorMap';

interface SensorData {
  sensor_id: string;
  sensor_type: string;
  timestamp: string;
  value: number;
  unit: string;
  location?: {
    latitude: number;
    longitude: number;
    altitude?: number;
    name?: string;
  };
  metadata?: Record<string, any>;
}

const SensorMapPage: React.FC = () => {
  const [sensors, setSensors] = useState<SensorData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTypes, setSelectedTypes] = useState<string[]>([
    'air_quality',
    'noise',
    'traffic',
    'light',
  ]);

  useEffect(() => {
    const fetchSensors = async () => {
      setIsLoading(true);
      try {
        const response = await axios.get('/api/mesh');
        setSensors(response.data.sensor_readings);
        setError(null);
      } catch (err) {
        setError('Failed to fetch sensor data');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSensors();
    
    // Set up polling every 30 seconds
    const intervalId = setInterval(fetchSensors, 30000);
    
    // Clean up on unmount
    return () => clearInterval(intervalId);
  }, []);

  const handleTypeToggle = (type: string) => {
    setSelectedTypes(prev => {
      if (prev.includes(type)) {
        return prev.filter(t => t !== type);
      } else {
        return [...prev, type];
      }
    });
  };

  // Filter sensors by selected types
  const filteredSensors = sensors.filter(sensor => 
    selectedTypes.includes(sensor.sensor_type)
  );

  // Count sensors by type
  const sensorCounts = sensors.reduce((acc, sensor) => {
    acc[sensor.sensor_type] = (acc[sensor.sensor_type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Format the sensor type for display
  const formatSensorType = (type: string) => {
    return type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  return (
    <div>
      <h1 className="text-2xl font-semibold text-gray-900">Sensor Map</h1>
      
      {/* Filter Controls */}
      <div className="mt-4 bg-white p-4 rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900 mb-2">Filter Sensors</h2>
        <div className="flex flex-wrap gap-2">
          {Object.entries(sensorCounts).map(([type, count]) => (
            <button
              key={type}
              onClick={() => handleTypeToggle(type)}
              className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium ${
                selectedTypes.includes(type)
                  ? 'bg-primary-100 text-primary-800 border-primary-300'
                  : 'bg-gray-100 text-gray-800 border-gray-300'
              } border`}
            >
              <span className={`mr-1.5 h-2 w-2 rounded-full ${
                type === 'air_quality' ? 'bg-blue-500' :
                type === 'noise' ? 'bg-purple-500' :
                type === 'traffic' ? 'bg-green-500' :
                type === 'light' ? 'bg-yellow-500' : 'bg-gray-500'
              }`}></span>
              {formatSensorType(type)} ({count})
            </button>
          ))}
        </div>
      </div>
      
      {/* Map */}
      <div className="mt-4">
        {isLoading ? (
          <div className="bg-white p-12 rounded-lg shadow flex items-center justify-center">
            <p className="text-gray-500">Loading sensor data...</p>
          </div>
        ) : error ? (
          <div className="bg-white p-12 rounded-lg shadow">
            <p className="text-red-500">{error}</p>
          </div>
        ) : filteredSensors.length === 0 ? (
          <div className="bg-white p-12 rounded-lg shadow flex items-center justify-center">
            <p className="text-gray-500">No sensors found with the selected filters.</p>
          </div>
        ) : (
          <SensorMapComponent sensors={filteredSensors} height="600px" />
        )}
      </div>
      
      {/* Legend */}
      <div className="mt-4 bg-white p-4 rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900 mb-2">Map Legend</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex items-center">
            <div className="w-6 h-6 rounded-full bg-blue-500 mr-2"></div>
            <span>Air Quality Sensors</span>
          </div>
          <div className="flex items-center">
            <div className="w-6 h-6 rounded-full bg-purple-500 mr-2"></div>
            <span>Noise Sensors</span>
          </div>
          <div className="flex items-center">
            <div className="w-6 h-6 rounded-full bg-green-500 mr-2"></div>
            <span>Traffic Sensors</span>
          </div>
          <div className="flex items-center">
            <div className="w-6 h-6 rounded-full bg-yellow-500 mr-2"></div>
            <span>Light Sensors</span>
          </div>
        </div>
      </div>
      
      {/* Sensor Stats */}
      <div className="mt-4 mb-8 bg-white p-4 rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900 mb-2">Sensor Statistics</h2>
        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table className="min-w-full divide-y divide-gray-300">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Sensor Type</th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Count</th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Latest Value (Avg)</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {Object.entries(sensorCounts).map(([type, count]) => {
                // Calculate average value for this sensor type
                const sensorsOfType = sensors.filter(s => s.sensor_type === type);
                const avgValue = sensorsOfType.length > 0
                  ? sensorsOfType.reduce((sum, s) => sum + s.value, 0) / sensorsOfType.length
                  : 0;
                const unit = sensorsOfType.length > 0 ? sensorsOfType[0].unit : '';
                
                return (
                  <tr key={type}>
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                      {formatSensorType(type)}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{count}</td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                      {avgValue.toFixed(2)} {unit}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default SensorMapPage;
