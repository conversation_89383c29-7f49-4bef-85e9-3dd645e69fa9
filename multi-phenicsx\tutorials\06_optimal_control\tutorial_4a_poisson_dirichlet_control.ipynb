{"cells": [{"cell_type": "markdown", "id": "b25e7efb", "metadata": {}, "source": ["# Tutorial 06, case 4a: Poisson problem with Dirichlet control\n", "\n", "In this tutorial we solve the optimal control problem\n", "\n", "$$\\min J(y, u) = \\frac{1}{2} \\int_{\\Omega} (y - y_d)^2 dx + \\frac{\\alpha}{2} \\int_{\\Gamma_2} u^2 ds$$\n", "s.t.\n", "$$\\begin{cases}\n", "      - \\Delta y = f     & \\text{in } \\Omega\\\\\n", "    \\partial_n y = 0     & \\text{on } \\Gamma_1\\\\\n", "               y = u     & \\text{on } \\Gamma_2\\\\\n", "    \\partial_n y = 0     & \\text{on } \\Gamma_3\\\\\n", "               y = 0     & \\text{on } \\Gamma_4\\\\\n", "\\end{cases}$$\n", "\n", "where\n", "$$\\begin{align*}\n", "& \\Omega               & \\text{unit square}\\\\\n", "& \\Gamma_1             & \\text{bottom boundary of the square}\\\\\n", "& \\Gamma_2             & \\text{left boundary of the square}\\\\\n", "& \\Gamma_3             & \\text{top boundary of the square}\\\\\n", "& \\Gamma_4             & \\text{right boundary of the square}\\\\\n", "& u \\in L^2(\\Gamma_2)  & \\text{control variable}\\\\\n", "& y \\in H^1(\\Omega)    & \\text{state variable}\\\\\n", "& \\alpha > 0           & \\text{penalization parameter}\\\\\n", "& y_d                  & \\text{desired state}\\\\\n", "& f                    & \\text{forcing term}\n", "\\end{align*}$$\n", "using an adjoint formulation solved by a one shot approach"]}, {"cell_type": "code", "execution_count": null, "id": "ac3f76a8", "metadata": {}, "outputs": [], "source": ["import dolfinx.fem\n", "import dolfinx.fem.petsc\n", "import dolfinx.io\n", "import dolfinx.mesh\n", "import mpi4py.MPI\n", "import numpy as np\n", "import numpy.typing\n", "import petsc4py.PETSc\n", "import ufl\n", "import viskex"]}, {"cell_type": "code", "execution_count": null, "id": "a6f2a3e9", "metadata": {}, "outputs": [], "source": ["import multiphenicsx.fem\n", "import multiphenicsx.fem.petsc"]}, {"cell_type": "markdown", "id": "53c2d777", "metadata": {}, "source": ["### Mesh"]}, {"cell_type": "code", "execution_count": null, "id": "c15a455c", "metadata": {}, "outputs": [], "source": ["mesh = dolfinx.mesh.create_unit_square(mpi4py.MPI.COMM_WORLD, 32, 32)"]}, {"cell_type": "code", "execution_count": null, "id": "9c6c97b0-1baf-44b7-9ef3-651a55604ea9", "metadata": {}, "outputs": [], "source": ["# Create connectivities required by the rest of the code\n", "mesh.topology.create_connectivity(mesh.topology.dim - 1, mesh.topology.dim)"]}, {"cell_type": "code", "execution_count": null, "id": "9c6e8d0f", "metadata": {}, "outputs": [], "source": ["def bottom(x: np.typing.NDArray[np.float64]) -> np.typing.NDArray[np.bool_]:\n", "    \"\"\"Condition that defines the bottom boundary.\"\"\"\n", "    return abs(x[1] - 0.) < np.finfo(float).eps  # type: ignore[no-any-return]\n", "\n", "\n", "def left(x: np.typing.NDArray[np.float64]) -> np.typing.NDArray[np.bool_]:\n", "    \"\"\"Condition that defines the left boundary.\"\"\"\n", "    return abs(x[0] - 0.) < np.finfo(float).eps  # type: ignore[no-any-return]\n", "\n", "\n", "def top(x: np.typing.NDArray[np.float64]) -> np.typing.NDArray[np.bool_]:\n", "    \"\"\"Condition that defines the top boundary.\"\"\"\n", "    return abs(x[1] - 1.) < np.finfo(float).eps  # type: ignore[no-any-return]\n", "\n", "\n", "def right(x: np.typing.NDArray[np.float64]) -> np.typing.NDArray[np.bool_]:\n", "    \"\"\"Condition that defines the right boundary.\"\"\"\n", "    return abs(x[0] - 1.) < np.finfo(float).eps  # type: ignore[no-any-return]\n", "\n", "\n", "boundaries_entities = dict()\n", "boundaries_values = dict()\n", "for (boundary, boundary_id) in zip((bottom, left, top, right), (1, 2, 3, 4)):\n", "    boundaries_entities[boundary_id] = dolfinx.mesh.locate_entities_boundary(\n", "        mesh, mesh.topology.dim - 1, boundary)\n", "    boundaries_values[boundary_id] = np.full(\n", "        boundaries_entities[boundary_id].shape, boundary_id, dtype=np.int32)\n", "boundaries_entities_unsorted = np.hstack(list(boundaries_entities.values()))\n", "boundaries_values_unsorted = np.hstack(list(boundaries_values.values()))\n", "boundaries_entities_argsort = np.argsort(boundaries_entities_unsorted)\n", "boundaries_entities_sorted = boundaries_entities_unsorted[boundaries_entities_argsort]\n", "boundaries_values_sorted = boundaries_values_unsorted[boundaries_entities_argsort]\n", "boundaries = dolfinx.mesh.meshtags(\n", "    mesh, mesh.topology.dim - 1,\n", "    boundaries_entities_sorted, boundaries_values_sorted)\n", "boundaries.name = \"boundaries\""]}, {"cell_type": "code", "execution_count": null, "id": "e97d79b1", "metadata": {}, "outputs": [], "source": ["boundaries_2 = boundaries.indices[boundaries.values == 2]\n", "boundaries_4 = boundaries.indices[boundaries.values == 4]\n", "boundaries_24 = boundaries.indices[np.isin(boundaries.values, (2, 4))]"]}, {"cell_type": "code", "execution_count": null, "id": "d17e81cb", "metadata": {}, "outputs": [], "source": ["# Define associated measures\n", "ds = ufl.Measure(\"ds\", subdomain_data=boundaries)"]}, {"cell_type": "code", "execution_count": null, "id": "c21f996d", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_mesh(mesh)"]}, {"cell_type": "code", "execution_count": null, "id": "c8038805", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_mesh_tags(mesh, boundaries, \"boundaries\")"]}, {"cell_type": "markdown", "id": "bc292fad", "metadata": {}, "source": ["### Function spaces"]}, {"cell_type": "code", "execution_count": null, "id": "67150228", "metadata": {}, "outputs": [], "source": ["Y = dolfinx.fem.functionspace(mesh, (\"<PERSON><PERSON>nge\", 2))\n", "U = dolfinx.fem.functionspace(mesh, (\"<PERSON><PERSON>nge\", 2))\n", "L = U.clone()\n", "Q = Y.clone()"]}, {"cell_type": "markdown", "id": "2dae0b77", "metadata": {}, "source": ["### Restrictions"]}, {"cell_type": "code", "execution_count": null, "id": "3b25a783", "metadata": {}, "outputs": [], "source": ["dofs_Y = np.arange(0, Y.dofmap.index_map.size_local + Y.dofmap.index_map.num_ghosts)\n", "dofs_U = dolfinx.fem.locate_dofs_topological(U, boundaries.dim, boundaries_2)\n", "dofs_L = dofs_U\n", "dofs_Q = dofs_Y\n", "restriction_Y = multiphenicsx.fem.DofMapRestriction(<PERSON>.dofmap, dofs_Y)\n", "restriction_U = multiphenicsx.fem.DofMapRestriction(U.dofmap, dofs_U)\n", "restriction_L = multiphenicsx.fem.DofMapRestriction(<PERSON><PERSON>dof<PERSON>, dofs_L)\n", "restriction_Q = multiphenicsx.fem.DofMapRestriction(Q.dofmap, dofs_Q)\n", "restriction = [restriction_Y, restriction_U, restriction_L, restriction_Q]"]}, {"cell_type": "markdown", "id": "9a6bb59a", "metadata": {}, "source": ["### Trial and test functions"]}, {"cell_type": "code", "execution_count": null, "id": "4942a123", "metadata": {}, "outputs": [], "source": ["(y, u, l, p) = (ufl.TrialFunction(Y), ufl.TrialFunction(U), ufl.TrialFunction(L), ufl.TrialFunction(Q))\n", "(z, v, m, q) = (ufl.TestFunction(Y), ufl.TestFunction(U), ufl.TestFunction(L), ufl.TestFunction(Q))"]}, {"cell_type": "markdown", "id": "03c84838", "metadata": {}, "source": [" ### Problem data"]}, {"cell_type": "code", "execution_count": null, "id": "c8d6e7d0", "metadata": {}, "outputs": [], "source": ["alpha = 1.e-5\n", "y_d = 1.\n", "x = ufl.SpatialCoordinate(mesh)\n", "ff = 10 * ufl.sin(2 * ufl.pi * x[0]) * ufl.sin(2 * ufl.pi * x[1])\n", "bc0 = petsc4py.PETSc.ScalarType(0)"]}, {"cell_type": "markdown", "id": "01dc45b3", "metadata": {}, "source": ["### Optimality conditions"]}, {"cell_type": "code", "execution_count": null, "id": "d59f9c75", "metadata": {}, "outputs": [], "source": ["a = [[ufl.inner(y, z) * ufl.dx, None, ufl.inner(l, z) * ds(2), ufl.inner(ufl.grad(p), ufl.grad(z)) * ufl.dx],\n", "     [None, alpha * ufl.inner(u, v) * ds(2), - ufl.inner(l, v) * ds(2), None],\n", "     [ufl.inner(y, m) * ds(2), - ufl.inner(u, m) * ds(2), None, None],\n", "     [ufl.inner(ufl.grad(y), ufl.grad(q)) * ufl.dx, None, None, None]]\n", "f = [ufl.inner(y_d, z) * ufl.dx,\n", "     None,\n", "     None,\n", "     ufl.inner(ff, q) * ufl.dx]\n", "a[3][3] = dolfinx.fem.Constant(mesh, petsc4py.PETSc.ScalarType(0)) * ufl.inner(p, q) * ufl.dx\n", "f[1] = ufl.inner(dolfinx.fem.Constant(mesh, petsc4py.PETSc.ScalarType(0)), v) * ufl.dx\n", "f[2] = ufl.inner(dolfinx.fem.Constant(mesh, petsc4py.PETSc.ScalarType(0)), m) * ufl.dx\n", "a_cpp = dolfinx.fem.form(a)\n", "f_cpp = dolfinx.fem.form(f)\n", "bdofs_Y_4 = dolfinx.fem.locate_dofs_topological(Y, mesh.topology.dim - 1, boundaries_4)\n", "bdofs_Q_24 = dolfinx.fem.locate_dofs_topological(Q, mesh.topology.dim - 1, boundaries_24)\n", "bc = [dolfinx.fem.dirichletbc(bc0, bdofs_Y_4, Y),\n", "      dolfinx.fem.dirichletbc(bc0, bdofs_Q_24, Q)]"]}, {"cell_type": "markdown", "id": "82533a76", "metadata": {}, "source": ["### Solution"]}, {"cell_type": "code", "execution_count": null, "id": "31fcfeb0", "metadata": {}, "outputs": [], "source": ["(y, u, l, p) = (dolfinx.fem.Function(Y), dolfinx.fem.Function(U), dolfinx.fem.Function(L), dolfinx.fem.Function(Q))"]}, {"cell_type": "markdown", "id": "f1a33f06", "metadata": {}, "source": ["### Cost functional"]}, {"cell_type": "code", "execution_count": null, "id": "c7276e35", "metadata": {}, "outputs": [], "source": ["J = 0.5 * ufl.inner(y - y_d, y - y_d) * ufl.dx + 0.5 * alpha * ufl.inner(u, u) * ds(2)\n", "J_cpp = dolfinx.fem.form(J)"]}, {"cell_type": "markdown", "id": "8b2e95ce", "metadata": {}, "source": ["### Uncontrolled functional value"]}, {"cell_type": "code", "execution_count": null, "id": "3286b00b", "metadata": {}, "outputs": [], "source": ["# Extract state forms from the optimality conditions\n", "a_state = ufl.replace(a[3][0], {q: z})\n", "f_state = ufl.replace(f[3], {q: z})\n", "a_state_cpp = dolfinx.fem.form(a_state)\n", "f_state_cpp = dolfinx.fem.form(f_state)\n", "bdofs_Y_24 = dolfinx.fem.locate_dofs_topological(Y, mesh.topology.dim - 1, boundaries_24)\n", "bc_state = [dolfinx.fem.dirichletbc(bc0, bdofs_Y_24, Y)]"]}, {"cell_type": "code", "execution_count": null, "id": "8b006a1e", "metadata": {}, "outputs": [], "source": ["# Assemble the linear system for the state\n", "A_state = dolfinx.fem.petsc.assemble_matrix(a_state_cpp, bcs=bc_state)\n", "A_state.assemble()\n", "F_state = dolfinx.fem.petsc.assemble_vector(f_state_cpp)\n", "dolfinx.fem.petsc.apply_lifting(F_state, [a_state_cpp], [bc_state])\n", "F_state.ghostUpdate(addv=petsc4py.PETSc.InsertMode.ADD, mode=petsc4py.PETSc.ScatterMode.REVERSE)\n", "dolfinx.fem.petsc.set_bc(F_state, bc_state)"]}, {"cell_type": "code", "execution_count": null, "id": "4545d5ee", "metadata": {}, "outputs": [], "source": ["# Solve\n", "ksp = petsc4py.PETSc.KSP()\n", "ksp.create(mesh.comm)\n", "ksp.setOperators(A_state)\n", "ksp.setType(\"preonly\")\n", "ksp.getPC().setType(\"lu\")\n", "ksp.getPC().setFactorSolverType(\"mumps\")\n", "ksp.setFromOptions()\n", "ksp.solve(F_state, y.x.petsc_vec)\n", "y.x.petsc_vec.ghostUpdate(addv=petsc4py.PETSc.InsertMode.INSERT, mode=petsc4py.PETSc.ScatterMode.FORWARD)\n", "ksp.destroy()"]}, {"cell_type": "code", "execution_count": null, "id": "3dda8010", "metadata": {}, "outputs": [], "source": ["J_uncontrolled = mesh.comm.allreduce(dolfinx.fem.assemble_scalar(J_cpp), op=mpi4py.MPI.SUM)\n", "print(\"Uncontrolled J =\", J_uncontrolled)\n", "assert np.isclose(J_uncontrolled, 0.5038977)"]}, {"cell_type": "code", "execution_count": null, "id": "0d798c6d", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(y, \"uncontrolled state\")"]}, {"cell_type": "markdown", "id": "2d2bdba8", "metadata": {}, "source": ["### Optimal control"]}, {"cell_type": "code", "execution_count": null, "id": "8ed8cb22", "metadata": {}, "outputs": [], "source": ["# Assemble the block linear system for the optimality conditions\n", "A = multiphenicsx.fem.petsc.assemble_matrix_block(a_cpp, bcs=bc, restriction=(restriction, restriction))\n", "A.assemble()\n", "F = multiphenicsx.fem.petsc.assemble_vector_block(f_cpp, a_cpp, bcs=bc, restriction=restriction)"]}, {"cell_type": "code", "execution_count": null, "id": "386ef25b", "metadata": {}, "outputs": [], "source": ["# Solve\n", "yulp = multiphenicsx.fem.petsc.create_vector_block(f_cpp, restriction=restriction)\n", "ksp = petsc4py.PETSc.KSP()\n", "ksp.create(mesh.comm)\n", "ksp.setOperators(A)\n", "ksp.setType(\"preonly\")\n", "ksp.getPC().setType(\"lu\")\n", "ksp.getPC().setFactorSolverType(\"mumps\")\n", "ksp.setFromOptions()\n", "ksp.solve(F, yulp)\n", "yulp.ghostUpdate(addv=petsc4py.PETSc.InsertMode.INSERT, mode=petsc4py.PETSc.ScatterMode.FORWARD)\n", "ksp.destroy()"]}, {"cell_type": "code", "execution_count": null, "id": "ba0bf10b", "metadata": {}, "outputs": [], "source": ["# Split the block solution in components\n", "with multiphenicsx.fem.petsc.BlockVecSubVectorWrapper(\n", "        yulp, [<PERSON><PERSON>do<PERSON>, <PERSON><PERSON>do<PERSON>, <PERSON><PERSON>do<PERSON>, <PERSON><PERSON>dofma<PERSON>], restriction) as yulp_wrapper:\n", "    for yulp_wrapper_local, component in zip(yulp_wrapper, (y, u, l, p)):\n", "        with component.x.petsc_vec.localForm() as component_local:\n", "            component_local[:] = yulp_wrapper_local"]}, {"cell_type": "code", "execution_count": null, "id": "2b58d0b9", "metadata": {}, "outputs": [], "source": ["J_controlled = mesh.comm.allreduce(dolfinx.fem.assemble_scalar(J_cpp), op=mpi4py.MPI.SUM)\n", "print(\"Optimal J =\", J_controlled)\n", "assert np.isclose(J_controlled, 0.1281224)"]}, {"cell_type": "code", "execution_count": null, "id": "f8699a4a", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(y, \"state\")"]}, {"cell_type": "code", "execution_count": null, "id": "332ec4a7", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(u, \"control\")"]}, {"cell_type": "code", "execution_count": null, "id": "70268ff2", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(l, \"lambda\")"]}, {"cell_type": "code", "execution_count": null, "id": "40194df7", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(p, \"adjoint\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython"}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python"}}, "nbformat": 4, "nbformat_minor": 5}