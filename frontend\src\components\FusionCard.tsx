import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowsPointingInIcon } from '@heroicons/react/24/outline';

interface FusionCardProps {
  rule: {
    name: string;
    description?: string;
    operator: string;
    inputs: string[];
  };
}

const FusionCard: React.FC<FusionCardProps> = ({ rule }) => {
  // Format the operator for display
  const formatOperator = (operator: string) => {
    return operator.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  // Format the sensor type for display
  const formatSensorType = (type: string) => {
    return type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  // Get the appropriate color based on operator
  const getOperatorColor = (operator: string) => {
    switch (operator) {
      case 'multiply':
        return 'text-blue-500';
      case 'divide':
        return 'text-red-500';
      case 'add':
        return 'text-green-500';
      case 'subtract':
        return 'text-yellow-500';
      case 'power':
        return 'text-purple-500';
      case 'weighted_average':
        return 'text-indigo-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0 bg-primary-100 rounded-md p-3">
            <ArrowsPointingInIcon className="h-6 w-6 text-primary-600" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <h3 className="text-lg font-medium text-gray-900 truncate">{rule.name}</h3>
            {rule.description && (
              <p className="mt-1 text-sm text-gray-500">{rule.description}</p>
            )}
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-500">Operator:</span>
            <span className={`ml-2 text-sm font-medium ${getOperatorColor(rule.operator)}`}>
              {formatOperator(rule.operator)}
            </span>
          </div>
          <div className="mt-2">
            <span className="text-sm font-medium text-gray-500">Inputs:</span>
            <div className="mt-1 flex flex-wrap gap-2">
              {rule.inputs.map((input, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                >
                  {formatSensorType(input)}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="bg-gray-50 px-4 py-4 sm:px-6">
        <div className="text-sm">
          <Link
            to={`/fusion-rules/${rule.name}`}
            className="font-medium text-primary-600 hover:text-primary-500"
          >
            View details
          </Link>
        </div>
      </div>
    </div>
  );
};

export default FusionCard;
