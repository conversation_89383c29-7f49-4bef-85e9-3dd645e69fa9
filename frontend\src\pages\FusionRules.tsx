import React, { useState } from 'react';
import { useFusionData } from '../hooks/useFusionData';
import FusionCard from '../components/FusionCard';
import { PlusIcon } from '@heroicons/react/24/outline';
import axios from 'axios';

interface NewRuleFormData {
  name: string;
  description: string;
  operator: string;
  inputs: string[];
  weights: string;
}

const FusionRules: React.FC = () => {
  const { fusionData, isLoading, error, refetch } = useFusionData();
  const [showNewRuleForm, setShowNewRuleForm] = useState(false);
  const [formData, setFormData] = useState<NewRuleFormData>({
    name: '',
    description: '',
    operator: 'multiply',
    inputs: [],
    weights: '',
  });
  const [formError, setFormError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleInputsChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const options = Array.from(e.target.selectedOptions).map(option => option.value);
    setFormData(prev => ({ ...prev, inputs: options }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    try {
      // Parse weights from comma-separated string to array of numbers
      const weights = formData.weights
        ? formData.weights.split(',').map(w => parseFloat(w.trim()))
        : undefined;

      // Create the rule object
      const rule = {
        name: formData.name,
        description: formData.description || undefined,
        operator: formData.operator,
        inputs: formData.inputs,
        weights,
      };

      // Submit to API
      await axios.post('/api/mesh/rules', rule);
      
      // Reset form and refresh data
      setFormData({
        name: '',
        description: '',
        operator: 'multiply',
        inputs: [],
        weights: '',
      });
      setShowNewRuleForm(false);
      refetch();
    } catch (err) {
      setFormError('Failed to create fusion rule. Please check your inputs and try again.');
      console.error(err);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900">Fusion Rules</h1>
        <button
          type="button"
          onClick={() => setShowNewRuleForm(!showNewRuleForm)}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
          New Rule
        </button>
      </div>

      {/* New Rule Form */}
      {showNewRuleForm && (
        <div className="mt-6 bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Create New Fusion Rule</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>Define a new rule for fusing sensor data.</p>
            </div>
            {formError && (
              <div className="mt-2 text-sm text-red-600">
                {formError}
              </div>
            )}
            <form className="mt-5 space-y-4" onSubmit={handleSubmit}>
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Rule Name
                </label>
                <input
                  type="text"
                  name="name"
                  id="name"
                  required
                  value={formData.name}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder="urban_comfort"
                />
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  name="description"
                  id="description"
                  rows={3}
                  value={formData.description}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder="Urban comfort index based on air quality, noise, and traffic"
                />
              </div>

              <div>
                <label htmlFor="operator" className="block text-sm font-medium text-gray-700">
                  Operator
                </label>
                <select
                  id="operator"
                  name="operator"
                  value={formData.operator}
                  onChange={handleInputChange}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                >
                  <option value="multiply">Multiply</option>
                  <option value="divide">Divide</option>
                  <option value="add">Add</option>
                  <option value="subtract">Subtract</option>
                  <option value="power">Power</option>
                  <option value="weighted_average">Weighted Average</option>
                </select>
              </div>

              <div>
                <label htmlFor="inputs" className="block text-sm font-medium text-gray-700">
                  Inputs
                </label>
                <select
                  id="inputs"
                  name="inputs"
                  multiple
                  required
                  value={formData.inputs}
                  onChange={handleInputsChange}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                >
                  <option value="air_quality">Air Quality</option>
                  <option value="noise">Noise</option>
                  <option value="traffic">Traffic</option>
                  <option value="light">Light</option>
                  <option value="temperature">Temperature</option>
                  <option value="humidity">Humidity</option>
                </select>
                <p className="mt-1 text-xs text-gray-500">Hold Ctrl/Cmd to select multiple</p>
              </div>

              <div>
                <label htmlFor="weights" className="block text-sm font-medium text-gray-700">
                  Weights (comma-separated)
                </label>
                <input
                  type="text"
                  name="weights"
                  id="weights"
                  value={formData.weights}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder="1.0, -1.0, -0.5"
                />
                <p className="mt-1 text-xs text-gray-500">Optional. Must match the number of inputs.</p>
              </div>

              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowNewRuleForm(false)}
                  className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Create
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Rules List */}
      <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {isLoading ? (
          <p>Loading fusion rules...</p>
        ) : error ? (
          <p className="text-red-500">Error loading fusion rules</p>
        ) : fusionData && fusionData.length > 0 ? (
          fusionData.map((rule) => (
            <FusionCard key={rule.name} rule={rule} />
          ))
        ) : (
          <p>No fusion rules found. Create one to get started.</p>
        )}
      </div>
    </div>
  );
};

export default FusionRules;
