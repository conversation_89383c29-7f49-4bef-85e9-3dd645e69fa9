"""Fusion operators for combining sensor data using multiphenicsx."""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from dolfinx import fem, mesh
from mpi4py import MPI
from multiphenicsx.fem import DofMapRestriction
import multiphenicsx.fem.petsc
import ufl

from enviromesh.core.models import (
    FusionOperator,
    FusionResult,
    FusionRule,
    SensorReading,
    SensorType,
)

logger = logging.getLogger(__name__)


class FusionEngine:
    """Engine for fusing sensor data using multiphenicsx."""

    def __init__(self) -> None:
        """Initialize the fusion engine."""
        self.comm = MPI.COMM_WORLD
        self._rules: Dict[str, FusionRule] = {}
        self._cached_meshes: Dict[str, mesh.Mesh] = {}
        self._cached_function_spaces: Dict[str, fem.FunctionSpace] = {}

    def register_rule(self, rule: FusionRule) -> None:
        """Register a fusion rule with the engine.

        Args:
            rule: The fusion rule to register.
        """
        self._rules[rule.name] = rule
        logger.info(f"Registered fusion rule: {rule.name}")

    def get_rule(self, rule_name: str) -> FusionRule:
        """Get a fusion rule by name.

        Args:
            rule_name: The name of the rule to get.

        Returns:
            The fusion rule.

        Raises:
            KeyError: If the rule does not exist.
        """
        if rule_name not in self._rules:
            raise KeyError(f"Fusion rule not found: {rule_name}")
        return self._rules[rule_name]

    def list_rules(self) -> List[FusionRule]:
        """List all registered fusion rules.

        Returns:
            A list of all registered fusion rules.
        """
        return list(self._rules.values())

    def _prepare_mesh(self, data_points: int) -> Tuple[mesh.Mesh, fem.FunctionSpace]:
        """Prepare a mesh for the fusion operation.

        Args:
            data_points: The number of data points to create in the mesh.

        Returns:
            A tuple containing the mesh and function space.
        """
        # Create a simple 1D mesh with data_points cells
        key = f"mesh_{data_points}"
        if key in self._cached_meshes and key in self._cached_function_spaces:
            return self._cached_meshes[key], self._cached_function_spaces[key]

        # Create a 1D mesh with data_points cells
        domain_mesh = mesh.create_unit_interval(self.comm, data_points)
        V = fem.functionspace(domain_mesh, ("Lagrange", 1))

        # Cache the mesh and function space
        self._cached_meshes[key] = domain_mesh
        self._cached_function_spaces[key] = V

        return domain_mesh, V

    def _apply_operator(
        self,
        operator: FusionOperator,
        values: List[np.ndarray],
        weights: Optional[List[float]] = None,
        parameters: Optional[Dict[str, Any]] = None,
    ) -> np.ndarray:
        """Apply a fusion operator to a list of values.

        Args:
            operator: The fusion operator to apply.
            values: The values to fuse.
            weights: Optional weights for the values.
            parameters: Optional parameters for the fusion operation.

        Returns:
            The fused values.

        Raises:
            ValueError: If the operator is not supported.
        """
        if parameters is None:
            parameters = {}

        if weights is None:
            weights = [1.0] * len(values)

        if len(values) != len(weights):
            raise ValueError("Number of values must match number of weights")

        if operator == FusionOperator.MULTIPLY:
            result = np.ones_like(values[0])
            for i, value in enumerate(values):
                if weights[i] != 0:
                    result *= value ** weights[i]
            return result

        elif operator == FusionOperator.DIVIDE:
            if len(values) != 2:
                raise ValueError("Division requires exactly 2 inputs")
            return values[0] ** weights[0] / values[1] ** weights[1]

        elif operator == FusionOperator.ADD:
            result = np.zeros_like(values[0])
            for i, value in enumerate(values):
                result += value * weights[i]
            return result

        elif operator == FusionOperator.SUBTRACT:
            if len(values) != 2:
                raise ValueError("Subtraction requires exactly 2 inputs")
            return values[0] * weights[0] - values[1] * weights[1]

        elif operator == FusionOperator.POWER:
            if len(values) != 1:
                raise ValueError("Power requires exactly 1 input")
            power = parameters.get("power", weights[0])
            return values[0] ** power

        elif operator == FusionOperator.WEIGHTED_AVERAGE:
            total_weight = sum(weights)
            if total_weight == 0:
                raise ValueError("Sum of weights cannot be zero")
            result = np.zeros_like(values[0])
            for i, value in enumerate(values):
                result += value * weights[i]
            return result / total_weight

        elif operator == FusionOperator.CUSTOM:
            # Custom operator requires a 'function' parameter
            if "function" not in parameters:
                raise ValueError("Custom operator requires a 'function' parameter")
            custom_func = parameters["function"]
            return custom_func(values, weights, parameters)

        else:
            raise ValueError(f"Unsupported fusion operator: {operator}")

    def fuse(
        self,
        rule_name: str,
        readings: Dict[SensorType, List[SensorReading]],
    ) -> FusionResult:
        """Fuse sensor readings according to a rule.

        Args:
            rule_name: The name of the fusion rule to apply.
            readings: A dictionary mapping sensor types to lists of readings.

        Returns:
            The result of the fusion operation.

        Raises:
            KeyError: If the rule does not exist.
            ValueError: If the required sensor types are not provided.
        """
        rule = self.get_rule(rule_name)

        # Check that all required sensor types are provided
        for sensor_type in rule.inputs:
            if sensor_type not in readings or not readings[sensor_type]:
                raise ValueError(f"Missing readings for sensor type: {sensor_type}")

        # Prepare the data for fusion
        input_values: Dict[SensorType, np.ndarray] = {}
        input_timestamps: Dict[SensorType, List[datetime]] = {}
        all_readings: List[SensorReading] = []

        for sensor_type in rule.inputs:
            sensor_readings = readings[sensor_type]
            all_readings.extend(sensor_readings)
            values = np.array([reading.value for reading in sensor_readings])
            timestamps = [reading.timestamp for reading in sensor_readings]
            input_values[sensor_type] = values
            input_timestamps[sensor_type] = timestamps

        # Apply the fusion operator
        values_list = [input_values[sensor_type] for sensor_type in rule.inputs]
        fused_value = self._apply_operator(
            rule.operator,
            values_list,
            rule.weights,
            rule.parameters,
        )

        # For simplicity, use the mean of the fused values
        result_value = float(np.mean(fused_value))

        # Use the latest timestamp from all readings
        latest_timestamp = max(
            [max(input_timestamps[sensor_type]) for sensor_type in rule.inputs]
        )

        return FusionResult(
            rule_name=rule_name,
            timestamp=latest_timestamp,
            value=result_value,
            input_readings=all_readings,
            metadata={"raw_values": fused_value.tolist()},
        )
