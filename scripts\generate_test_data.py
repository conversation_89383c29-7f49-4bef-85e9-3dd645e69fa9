#!/usr/bin/env python3
"""Script to generate test data for EnviroMesh."""

import argparse
import json
import logging
import random
import time
from datetime import datetime, timedelta

import paho.mqtt.client as mqtt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def generate_air_quality_data(sensor_id: str) -> dict:
    """Generate random air quality data.

    Args:
        sensor_id: The sensor ID.

    Returns:
        A dictionary containing the sensor reading.
    """
    return {
        "sensor_id": sensor_id,
        "sensor_type": "air_quality",
        "timestamp": datetime.utcnow().isoformat(),
        "value": random.uniform(0, 500),  # AQI value
        "unit": "AQI",
        "location": {
            "latitude": random.uniform(40.7, 40.8),
            "longitude": random.uniform(-74.0, -73.9),
            "altitude": random.uniform(0, 100),
            "name": f"Location {sensor_id}",
        },
        "metadata": {
            "pm25": random.uniform(0, 100),
            "pm10": random.uniform(0, 150),
            "o3": random.uniform(0, 0.2),
            "no2": random.uniform(0, 0.2),
            "co": random.uniform(0, 10),
        },
    }


def generate_noise_data(sensor_id: str) -> dict:
    """Generate random noise data.

    Args:
        sensor_id: The sensor ID.

    Returns:
        A dictionary containing the sensor reading.
    """
    return {
        "sensor_id": sensor_id,
        "sensor_type": "noise",
        "timestamp": datetime.utcnow().isoformat(),
        "value": random.uniform(30, 100),  # dB value
        "unit": "dB",
        "location": {
            "latitude": random.uniform(40.7, 40.8),
            "longitude": random.uniform(-74.0, -73.9),
            "altitude": random.uniform(0, 100),
            "name": f"Location {sensor_id}",
        },
        "metadata": {
            "frequency_spectrum": {
                "low": random.uniform(30, 60),
                "mid": random.uniform(40, 70),
                "high": random.uniform(50, 80),
            },
            "source": random.choice(["traffic", "construction", "people", "unknown"]),
        },
    }


def generate_traffic_data(sensor_id: str) -> dict:
    """Generate random traffic data.

    Args:
        sensor_id: The sensor ID.

    Returns:
        A dictionary containing the sensor reading.
    """
    return {
        "sensor_id": sensor_id,
        "sensor_type": "traffic",
        "timestamp": datetime.utcnow().isoformat(),
        "value": random.uniform(0, 100),  # Vehicles per minute
        "unit": "vehicles/min",
        "location": {
            "latitude": random.uniform(40.7, 40.8),
            "longitude": random.uniform(-74.0, -73.9),
            "altitude": random.uniform(0, 100),
            "name": f"Location {sensor_id}",
        },
        "metadata": {
            "vehicle_types": {
                "cars": random.uniform(0, 80),
                "trucks": random.uniform(0, 20),
                "buses": random.uniform(0, 10),
                "bicycles": random.uniform(0, 5),
            },
            "congestion_level": random.choice(["low", "medium", "high"]),
        },
    }


def generate_light_data(sensor_id: str) -> dict:
    """Generate random light data.

    Args:
        sensor_id: The sensor ID.

    Returns:
        A dictionary containing the sensor reading.
    """
    return {
        "sensor_id": sensor_id,
        "sensor_type": "light",
        "timestamp": datetime.utcnow().isoformat(),
        "value": random.uniform(0, 100000),  # Lux value
        "unit": "lux",
        "location": {
            "latitude": random.uniform(40.7, 40.8),
            "longitude": random.uniform(-74.0, -73.9),
            "altitude": random.uniform(0, 100),
            "name": f"Location {sensor_id}",
        },
        "metadata": {
            "color_temperature": random.uniform(2000, 6500),  # Kelvin
            "source": random.choice(["natural", "artificial", "mixed"]),
        },
    }


def main() -> None:
    """Run the test data generator."""
    parser = argparse.ArgumentParser(description="Generate test data for EnviroMesh")
    parser.add_argument(
        "--broker", type=str, default="localhost", help="MQTT broker address"
    )
    parser.add_argument("--port", type=int, default=1883, help="MQTT broker port")
    parser.add_argument(
        "--username", type=str, default=None, help="MQTT broker username"
    )
    parser.add_argument(
        "--password", type=str, default=None, help="MQTT broker password"
    )
    parser.add_argument(
        "--interval", type=float, default=1.0, help="Interval between messages in seconds"
    )
    parser.add_argument(
        "--duration", type=int, default=60, help="Duration to run in seconds"
    )
    args = parser.parse_args()

    # Connect to MQTT broker
    client = mqtt.Client(client_id="enviromesh-test-data-generator")
    if args.username and args.password:
        client.username_pw_set(args.username, args.password)

    try:
        client.connect(args.broker, args.port, 60)
        client.loop_start()
        logger.info(f"Connected to MQTT broker at {args.broker}:{args.port}")
    except Exception as e:
        logger.error(f"Failed to connect to MQTT broker: {e}")
        return

    # Generate and publish test data
    start_time = time.time()
    end_time = start_time + args.duration

    try:
        while time.time() < end_time:
            # Generate data for different sensor types
            for sensor_type in ["air_quality", "noise", "traffic", "light"]:
                for i in range(1, 4):  # 3 sensors of each type
                    sensor_id = f"{sensor_type}_{i}"
                    topic = f"sensors/{sensor_type}/{sensor_id}"

                    # Generate data based on sensor type
                    if sensor_type == "air_quality":
                        data = generate_air_quality_data(sensor_id)
                    elif sensor_type == "noise":
                        data = generate_noise_data(sensor_id)
                    elif sensor_type == "traffic":
                        data = generate_traffic_data(sensor_id)
                    elif sensor_type == "light":
                        data = generate_light_data(sensor_id)
                    else:
                        continue

                    # Publish to MQTT
                    payload = json.dumps(data)
                    client.publish(topic, payload)
                    logger.info(f"Published to {topic}: {payload}")

            # Wait for the next interval
            time.sleep(args.interval)

    except KeyboardInterrupt:
        logger.info("Test data generation interrupted")
    finally:
        # Disconnect from MQTT broker
        client.loop_stop()
        client.disconnect()
        logger.info("Disconnected from MQTT broker")


if __name__ == "__main__":
    main()
