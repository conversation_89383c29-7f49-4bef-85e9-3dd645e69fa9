import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  AreaChart, Area
} from 'recharts';
import { format } from 'date-fns';
import SensorCard from '../components/SensorCard';
import FusionCard from '../components/FusionCard';
import InsightCard from '../components/InsightCard';
import { useWebSocket } from '../hooks/useWebSocket';
import { useSensorData } from '../hooks/useSensorData';
import { useFusionData } from '../hooks/useFusionData';

const Dashboard: React.FC = () => {
  const [realtimeData, setRealtimeData] = useState<any[]>([]);
  const { lastMessage } = useWebSocket('ws://localhost:8000/ws');
  const { sensorData, isLoading: isSensorLoading } = useSensorData();
  const { fusionData, isLoading: isFusionLoading } = useFusionData();

  useEffect(() => {
    if (lastMessage) {
      try {
        const data = JSON.parse(lastMessage.data);
        if (data.type === 'sensor_reading') {
          setRealtimeData(prev => {
            // Keep only the last 50 readings
            const newData = [...prev, {
              ...data.data,
              timestamp: new Date(data.data.timestamp)
            }];
            if (newData.length > 50) {
              return newData.slice(-50);
            }
            return newData;
          });
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    }
  }, [lastMessage]);

  // Group sensor data by type
  const sensorsByType = sensorData?.reduce((acc, sensor) => {
    if (!acc[sensor.sensor_type]) {
      acc[sensor.sensor_type] = [];
    }
    acc[sensor.sensor_type].push(sensor);
    return acc;
  }, {} as Record<string, any[]>) || {};

  return (
    <div>
      <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>

      {/* Overview Stats */}
      <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-primary-500 rounded-md p-3">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Sensors</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {Object.values(sensorsByType).flat().length || 0}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-secondary-500 rounded-md p-3">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Fusion Rules</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {fusionData?.length || 0}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Data Points</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {realtimeData.length}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Insights Generated</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      3
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Real-time Chart */}
      <div className="mt-8">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900">Real-time Data</h2>
          <div className="mt-4 h-96">
            {realtimeData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={realtimeData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="timestamp"
                    tickFormatter={(timestamp) => format(new Date(timestamp), 'HH:mm:ss')}
                  />
                  <YAxis />
                  <Tooltip
                    labelFormatter={(timestamp) => format(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss')}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#8884d8"
                    activeDot={{ r: 8 }}
                    name="Sensor Value"
                  />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-full">
                <p className="text-gray-500">Waiting for real-time data...</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Sensor Cards */}
      <div className="mt-8">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900">Sensors</h2>
          <Link to="/sensor-map" className="text-primary-600 hover:text-primary-900">
            View map
          </Link>
        </div>
        <div className="mt-4 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
          {isSensorLoading ? (
            <p>Loading sensors...</p>
          ) : (
            Object.entries(sensorsByType).map(([type, sensors]) => (
              <SensorCard
                key={type}
                type={type}
                count={sensors.length}
                latestReading={sensors[0]}
              />
            ))
          )}
        </div>
      </div>

      {/* Fusion Rules */}
      <div className="mt-8">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900">Fusion Rules</h2>
          <Link to="/fusion-rules" className="text-primary-600 hover:text-primary-900">
            View all
          </Link>
        </div>
        <div className="mt-4 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
          {isFusionLoading ? (
            <p>Loading fusion rules...</p>
          ) : (
            fusionData?.slice(0, 3).map((rule) => (
              <FusionCard key={rule.name} rule={rule} />
            ))
          )}
        </div>
      </div>

      {/* Latest Insights */}
      <div className="mt-8 mb-12">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900">Latest Insights</h2>
          <Link to="/insights" className="text-primary-600 hover:text-primary-900">
            View all
          </Link>
        </div>
        <div className="mt-4">
          <InsightCard
            title="Urban Comfort Analysis"
            content="The urban comfort index shows a 15% improvement over the last 24 hours, primarily driven by reduced noise levels and improved air quality. Traffic remains consistent with previous patterns."
            date={new Date()}
            confidence={0.85}
          />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
