## multiphenicsx - easy prototyping of multiphysics problems in FEniCSx ##
<img src="https://multiphenics.github.io/_images/multiphenicsx-logo.png" alt="multiphenicsx - easy prototyping of multiphysics problems in FEniCSx" width="150px">

**multiphenicsx** is a python library that aims at providing tools in **FEniCSx** for an easy prototyping of multiphysics problems on conforming meshes. In particular, it facilitates the definition of subdomain/boundary restricted variables.

**multiphenicsx** is currently developed and maintained at [Università Cattolica del Sacro Cuore](https://www.unicatt.it/) by [<PERSON><PERSON> <PERSON>](https://www.francescoballarin.it).

Like all core **FEniCSx** components, **multiphenicsx** is freely available under the GNU LGPL, version 3.

Visit [multiphenics.github.io](https://multiphenics.github.io/) for more information.
