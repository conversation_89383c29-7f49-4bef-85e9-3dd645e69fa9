import React from 'react';
import { format } from 'date-fns';
import { LightBulbIcon } from '@heroicons/react/24/outline';

interface InsightCardProps {
  title: string;
  content: string;
  date: Date;
  confidence: number;
}

const InsightCard: React.FC<InsightCardProps> = ({ title, content, date, confidence }) => {
  // Format confidence as percentage
  const confidencePercentage = Math.round(confidence * 100);

  // Determine confidence color
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-500';
    if (confidence >= 0.6) return 'text-yellow-500';
    return 'text-red-500';
  };

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0 bg-yellow-100 rounded-md p-3">
            <LightBulbIcon className="h-6 w-6 text-yellow-600" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <h3 className="text-lg font-medium text-gray-900">{title}</h3>
            <p className="mt-1 text-sm text-gray-500">
              {format(date, 'MMM d, yyyy HH:mm')}
            </p>
          </div>
          <div className="ml-4 flex-shrink-0">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getConfidenceColor(confidence)} bg-opacity-10`}>
              {confidencePercentage}% confidence
            </span>
          </div>
        </div>
        <div className="mt-4">
          <p className="text-sm text-gray-500">{content}</p>
        </div>
      </div>
      <div className="bg-gray-50 px-4 py-4 sm:px-6">
        <div className="text-sm">
          <a
            href="#"
            className="font-medium text-primary-600 hover:text-primary-500"
          >
            View full insight
          </a>
        </div>
      </div>
    </div>
  );
};

export default InsightCard;
