{"cells": [{"cell_type": "markdown", "id": "fea2f81b", "metadata": {}, "source": ["# Tutorial 06, case 1a: Poisson problem with distributed control\n", "\n", "In this tutorial we solve the optimal control problem\n", "\n", "$$\\min J(y, u) = \\frac{1}{2} \\int_{\\Omega} (y - y_d)^2 dx + \\frac{\\alpha}{2} \\int_{\\Omega} u^2 dx$$\n", "s.t.\n", "$$\\begin{cases}\n", "- \\Delta y = f + u     & \\text{in } \\Omega\\\\\n", "         y = 0         & \\text{on } \\partial\\Omega\n", "\\end{cases}$$\n", "\n", "where\n", "$$\\begin{align*}\n", "& \\Omega               & \\text{unit square}\\\\\n", "& u \\in L^2(\\Omega)    & \\text{control variable}\\\\\n", "& y \\in H^1_0(\\Omega)  & \\text{state variable}\\\\\n", "& \\alpha > 0           & \\text{penalization parameter}\\\\\n", "& y_d                  & \\text{desired state}\\\\\n", "& f                    & \\text{forcing term}\n", "\\end{align*}$$\n", "using an adjoint formulation solved by a one shot approach"]}, {"cell_type": "code", "execution_count": null, "id": "16094b06", "metadata": {}, "outputs": [], "source": ["import dolfinx.fem\n", "import dolfinx.fem.petsc\n", "import dolfinx.io\n", "import dolfinx.mesh\n", "import mpi4py.MPI\n", "import numpy as np\n", "import numpy.typing\n", "import petsc4py.PETSc\n", "import ufl\n", "import viskex"]}, {"cell_type": "code", "execution_count": null, "id": "c9a451dc", "metadata": {}, "outputs": [], "source": ["import multiphenicsx.fem\n", "import multiphenicsx.fem.petsc"]}, {"cell_type": "markdown", "id": "96c87e67", "metadata": {}, "source": ["### Mesh"]}, {"cell_type": "code", "execution_count": null, "id": "ef8841a9", "metadata": {}, "outputs": [], "source": ["mesh = dolfinx.mesh.create_unit_square(mpi4py.MPI.COMM_WORLD, 32, 32)"]}, {"cell_type": "code", "execution_count": null, "id": "5b3a0e22-c390-436b-a800-d07ceded6829", "metadata": {}, "outputs": [], "source": ["# Create connectivities required by the rest of the code\n", "mesh.topology.create_connectivity(mesh.topology.dim - 1, mesh.topology.dim)"]}, {"cell_type": "code", "execution_count": null, "id": "0d062d95", "metadata": {}, "outputs": [], "source": ["def bottom(x: np.typing.NDArray[np.float64]) -> np.typing.NDArray[np.bool_]:\n", "    \"\"\"Condition that defines the bottom boundary.\"\"\"\n", "    return abs(x[1] - 0.) < np.finfo(float).eps  # type: ignore[no-any-return]\n", "\n", "\n", "def left(x: np.typing.NDArray[np.float64]) -> np.typing.NDArray[np.bool_]:\n", "    \"\"\"Condition that defines the left boundary.\"\"\"\n", "    return abs(x[0] - 0.) < np.finfo(float).eps  # type: ignore[no-any-return]\n", "\n", "\n", "def top(x: np.typing.NDArray[np.float64]) -> np.typing.NDArray[np.bool_]:\n", "    \"\"\"Condition that defines the top boundary.\"\"\"\n", "    return abs(x[1] - 1.) < np.finfo(float).eps  # type: ignore[no-any-return]\n", "\n", "\n", "def right(x: np.typing.NDArray[np.float64]) -> np.typing.NDArray[np.bool_]:\n", "    \"\"\"Condition that defines the right boundary.\"\"\"\n", "    return abs(x[0] - 1.) < np.finfo(float).eps  # type: ignore[no-any-return]\n", "\n", "\n", "boundaries_entities = dict()\n", "boundaries_values = dict()\n", "for (boundary, boundary_id) in zip((bottom, left, top, right), (1, 2, 3, 4)):\n", "    boundaries_entities[boundary_id] = dolfinx.mesh.locate_entities_boundary(\n", "        mesh, mesh.topology.dim - 1, boundary)\n", "    boundaries_values[boundary_id] = np.full(\n", "        boundaries_entities[boundary_id].shape, boundary_id, dtype=np.int32)\n", "boundaries_entities_unsorted = np.hstack(list(boundaries_entities.values()))\n", "boundaries_values_unsorted = np.hstack(list(boundaries_values.values()))\n", "boundaries_entities_argsort = np.argsort(boundaries_entities_unsorted)\n", "boundaries_entities_sorted = boundaries_entities_unsorted[boundaries_entities_argsort]\n", "boundaries_values_sorted = boundaries_values_unsorted[boundaries_entities_argsort]\n", "boundaries = dolfinx.mesh.meshtags(\n", "    mesh, mesh.topology.dim - 1,\n", "    boundaries_entities_sorted, boundaries_values_sorted)\n", "boundaries.name = \"boundaries\""]}, {"cell_type": "code", "execution_count": null, "id": "35907e6d", "metadata": {}, "outputs": [], "source": ["boundaries_1234 = boundaries.indices[np.isin(boundaries.values, (1, 2, 3, 4))]"]}, {"cell_type": "code", "execution_count": null, "id": "3a7f5a04", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_mesh(mesh)"]}, {"cell_type": "code", "execution_count": null, "id": "2e2eddca", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_mesh_tags(mesh, boundaries, \"boundaries\")"]}, {"cell_type": "markdown", "id": "7cb95306", "metadata": {}, "source": ["### Function spaces"]}, {"cell_type": "code", "execution_count": null, "id": "b89b4676", "metadata": {}, "outputs": [], "source": ["Y = dolfinx.fem.functionspace(mesh, (\"Lagrange\", 1))\n", "U = dolfinx.fem.functionspace(mesh, (\"Lagrange\", 1))\n", "Q = Y.clone()"]}, {"cell_type": "markdown", "id": "5e22e8ce", "metadata": {}, "source": ["### Trial and test functions"]}, {"cell_type": "code", "execution_count": null, "id": "af3fdaa2", "metadata": {}, "outputs": [], "source": ["(y, u, p) = (ufl.TrialFunction(Y), ufl.TrialFunction(U), ufl.TrialFunction(Q))\n", "(z, v, q) = (ufl.TestFunction(Y), ufl.TestFunction(U), ufl.TestFunction(Q))"]}, {"cell_type": "markdown", "id": "263c4745", "metadata": {}, "source": [" ### Problem data"]}, {"cell_type": "code", "execution_count": null, "id": "beecbedc", "metadata": {}, "outputs": [], "source": ["alpha = 1.e-5\n", "x = ufl.SpatialCoordinate(mesh)\n", "y_d = 10 * x[0] * (1 - x[0]) * x[1] * (1 - x[1])\n", "ff = dolfinx.fem.Constant(mesh, petsc4py.PETSc.ScalarType(0))\n", "bc0 = petsc4py.PETSc.ScalarType(0)"]}, {"cell_type": "markdown", "id": "e1611b39", "metadata": {}, "source": ["### Optimality conditions"]}, {"cell_type": "code", "execution_count": null, "id": "da86e8a9", "metadata": {}, "outputs": [], "source": ["a = [[ufl.inner(y, z) * ufl.dx, None, ufl.inner(ufl.grad(p), ufl.grad(z)) * ufl.dx],\n", "     [None, alpha * ufl.inner(u, v) * ufl.dx, - ufl.inner(p, v) * ufl.dx],\n", "     [ufl.inner(ufl.grad(y), ufl.grad(q)) * ufl.dx, - ufl.inner(u, q) * ufl.dx, None]]\n", "f = [ufl.inner(y_d, z) * ufl.dx,\n", "     None,\n", "     ufl.inner(ff, q) * ufl.dx]\n", "a[2][2] = dolfinx.fem.Constant(mesh, petsc4py.PETSc.ScalarType(0)) * ufl.inner(p, q) * ufl.dx\n", "f[1] = ufl.inner(dolfinx.fem.Constant(mesh, petsc4py.PETSc.ScalarType(0)), v) * ufl.dx\n", "a_cpp = dolfinx.fem.form(a)\n", "f_cpp = dolfinx.fem.form(f)\n", "bdofs_Y_1234 = dolfinx.fem.locate_dofs_topological(Y, mesh.topology.dim - 1, boundaries_1234)\n", "bdofs_Q_1234 = dolfinx.fem.locate_dofs_topological(Q, mesh.topology.dim - 1, boundaries_1234)\n", "bc = [dolfinx.fem.dirichletbc(bc0, bdofs_Y_1234, Y),\n", "      dolfinx.fem.dirichletbc(bc0, bdofs_Q_1234, Q)]"]}, {"cell_type": "markdown", "id": "8a8501eb", "metadata": {}, "source": ["### Solution"]}, {"cell_type": "code", "execution_count": null, "id": "ba971a3f", "metadata": {}, "outputs": [], "source": ["(y, u, p) = (dolfinx.fem.Function(Y), dolfinx.fem.Function(U), dolfinx.fem.Function(Q))"]}, {"cell_type": "markdown", "id": "fbc4ba59", "metadata": {}, "source": ["### Cost functional"]}, {"cell_type": "code", "execution_count": null, "id": "66291bf8", "metadata": {}, "outputs": [], "source": ["J = 0.5 * ufl.inner(y - y_d, y - y_d) * ufl.dx + 0.5 * alpha * ufl.inner(u, u) * ufl.dx\n", "J_cpp = dolfinx.fem.form(J)"]}, {"cell_type": "markdown", "id": "eaf7bfbb", "metadata": {}, "source": ["### Uncontrolled functional value"]}, {"cell_type": "code", "execution_count": null, "id": "42969c51", "metadata": {}, "outputs": [], "source": ["# Extract state forms from the optimality conditions\n", "a_state = ufl.replace(a[2][0], {q: z})\n", "f_state = ufl.replace(f[2], {q: z})\n", "a_state_cpp = dolfinx.fem.form(a_state)\n", "f_state_cpp = dolfinx.fem.form(f_state)\n", "bc_state = [bc[0]]"]}, {"cell_type": "code", "execution_count": null, "id": "3d82111c", "metadata": {}, "outputs": [], "source": ["# Assemble the linear system for the state\n", "A_state = dolfinx.fem.petsc.assemble_matrix(a_state_cpp, bcs=bc_state)\n", "A_state.assemble()\n", "F_state = dolfinx.fem.petsc.assemble_vector(f_state_cpp)\n", "dolfinx.fem.petsc.apply_lifting(F_state, [a_state_cpp], [bc_state])\n", "F_state.ghostUpdate(addv=petsc4py.PETSc.InsertMode.ADD, mode=petsc4py.PETSc.ScatterMode.REVERSE)\n", "dolfinx.fem.petsc.set_bc(F_state, bc_state)"]}, {"cell_type": "code", "execution_count": null, "id": "e32ea23a", "metadata": {}, "outputs": [], "source": ["# Solve\n", "ksp = petsc4py.PETSc.KSP()\n", "ksp.create(mesh.comm)\n", "ksp.setOperators(A_state)\n", "ksp.setType(\"preonly\")\n", "ksp.getPC().setType(\"lu\")\n", "ksp.getPC().setFactorSolverType(\"mumps\")\n", "ksp.setFromOptions()\n", "ksp.solve(F_state, y.x.petsc_vec)\n", "y.x.petsc_vec.ghostUpdate(addv=petsc4py.PETSc.InsertMode.INSERT, mode=petsc4py.PETSc.ScatterMode.FORWARD)\n", "ksp.destroy()"]}, {"cell_type": "code", "execution_count": null, "id": "382a580b", "metadata": {}, "outputs": [], "source": ["J_uncontrolled = mesh.comm.allreduce(dolfinx.fem.assemble_scalar(J_cpp), op=mpi4py.MPI.SUM)\n", "print(\"Uncontrolled J =\", J_uncontrolled)\n", "assert np.isclose(J_uncontrolled, 0.055555555)"]}, {"cell_type": "code", "execution_count": null, "id": "a60fe45a", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(y, \"uncontrolled state\")"]}, {"cell_type": "markdown", "id": "fbc6b778", "metadata": {}, "source": ["### Optimal control"]}, {"cell_type": "code", "execution_count": null, "id": "43842b6c", "metadata": {}, "outputs": [], "source": ["# Assemble the block linear system for the optimality conditions\n", "A = dolfinx.fem.petsc.assemble_matrix_block(a_cpp, bcs=bc)\n", "A.assemble()\n", "F = dolfinx.fem.petsc.assemble_vector_block(f_cpp, a_cpp, bcs=bc)"]}, {"cell_type": "code", "execution_count": null, "id": "7efb90f6", "metadata": {}, "outputs": [], "source": ["# Solve\n", "yup = dolfinx.fem.petsc.create_vector_block(f_cpp)\n", "ksp = petsc4py.PETSc.KSP()\n", "ksp.create(mesh.comm)\n", "ksp.setOperators(A)\n", "ksp.setType(\"preonly\")\n", "ksp.getPC().setType(\"lu\")\n", "ksp.getPC().setFactorSolverType(\"mumps\")\n", "ksp.setFromOptions()\n", "ksp.solve(F, yup)\n", "yup.ghostUpdate(addv=petsc4py.PETSc.InsertMode.INSERT, mode=petsc4py.PETSc.ScatterMode.FORWARD)\n", "ksp.destroy()"]}, {"cell_type": "code", "execution_count": null, "id": "6433cb69", "metadata": {}, "outputs": [], "source": ["# Split the block solution in components\n", "with multiphenicsx.fem.petsc.BlockVecSubVectorWrapper(yup, [<PERSON><PERSON>dofmap, <PERSON><PERSON>dofmap, <PERSON>.dofmap]) as yup_wrapper:\n", "    for yup_wrapper_local, component in zip(yup_wrapper, (y, u, p)):\n", "        with component.x.petsc_vec.localForm() as component_local:\n", "            component_local[:] = yup_wrapper_local"]}, {"cell_type": "code", "execution_count": null, "id": "fd2dd019", "metadata": {}, "outputs": [], "source": ["J_controlled = mesh.comm.allreduce(dolfinx.fem.assemble_scalar(J_cpp), op=mpi4py.MPI.SUM)\n", "print(\"Optimal J =\", J_controlled)\n", "assert np.isclose(J_controlled, 0.0002337096)"]}, {"cell_type": "code", "execution_count": null, "id": "43ef1e4e", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(y, \"state\")"]}, {"cell_type": "code", "execution_count": null, "id": "c3f46095", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(u, \"control\")"]}, {"cell_type": "code", "execution_count": null, "id": "8a78504c", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(p, \"adjoint\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython"}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python"}}, "nbformat": 4, "nbformat_minor": 5}