"""Main FastAPI application for EnviroMesh."""

import asyncio
import logging
import os
from typing import Dict, List, Optional

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware

from enviromesh.api.routes import batch, insight, mesh
from enviromesh.core.fusion import FusionEngine
from enviromesh.core.models import FusionOperator, FusionRule, SensorReading, SensorType
from enviromesh.core.storage import TimeSeriesStorage
from enviromesh.ingestion.mqtt_client import MQTTClient
from enviromesh.ingestion.http_client import HTTPClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create the FastAPI application
app = FastAPI(
    title="EnviroMesh API",
    description="API for the EnviroMesh adaptive network for environmental data fusion",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create the storage
storage = TimeSeriesStorage()

# Create the fusion engine
fusion_engine = FusionEngine()

# Create the MQTT client if configured
mqtt_client = None
mqtt_broker = os.environ.get("MQTT_BROKER")
if mqtt_broker:
    mqtt_client = MQTTClient(
        broker=mqtt_broker,
        port=int(os.environ.get("MQTT_PORT", "1883")),
        username=os.environ.get("MQTT_USERNAME"),
        password=os.environ.get("MQTT_PASSWORD"),
        client_id=os.environ.get("MQTT_CLIENT_ID", "enviromesh-client"),
        storage=storage,
    )

# Create the HTTP client
http_client = HTTPClient(storage=storage)

# WebSocket connection manager
class ConnectionManager:
    """Manager for WebSocket connections."""

    def __init__(self) -> None:
        """Initialize the connection manager."""
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket) -> None:
        """Connect a WebSocket client.

        Args:
            websocket: The WebSocket connection.
        """
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket client connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket) -> None:
        """Disconnect a WebSocket client.

        Args:
            websocket: The WebSocket connection.
        """
        self.active_connections.remove(websocket)
        logger.info(f"WebSocket client disconnected. Total connections: {len(self.active_connections)}")

    async def broadcast(self, message: Dict) -> None:
        """Broadcast a message to all connected clients.

        Args:
            message: The message to broadcast.
        """
        for connection in self.active_connections:
            try:
                await connection.send_json(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")


# Create the connection manager
manager = ConnectionManager()


@app.on_event("startup")
async def startup_event() -> None:
    """Initialize the application on startup."""
    # Initialize the database
    await storage.initialize()

    # Register default fusion rules
    urban_comfort_rule = FusionRule(
        name="urban_comfort",
        description="Urban comfort index based on air quality, noise, and traffic",
        operator=FusionOperator.MULTIPLY,
        inputs=[SensorType.AIR_QUALITY, SensorType.NOISE, SensorType.TRAFFIC],
        weights=[1.0, -1.0, -0.5],
    )
    fusion_engine.register_rule(urban_comfort_rule)

    # Connect to MQTT broker if configured
    if mqtt_client:
        try:
            mqtt_client.connect()
            # Subscribe to default topics
            mqtt_client.subscribe("sensors/#")
            # Register callback to broadcast readings to WebSocket clients
            mqtt_client.register_callback("sensors/#", lambda reading: asyncio.create_task(
                manager.broadcast({"type": "sensor_reading", "data": reading.dict()})
            ))
        except Exception as e:
            logger.error(f"Failed to connect to MQTT broker: {e}")


@app.on_event("shutdown")
async def shutdown_event() -> None:
    """Clean up resources on shutdown."""
    # Disconnect from MQTT broker if connected
    if mqtt_client:
        mqtt_client.disconnect()

    # Close HTTP client
    await http_client.close()


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket) -> None:
    """WebSocket endpoint for real-time updates.

    Args:
        websocket: The WebSocket connection.
    """
    await manager.connect(websocket)
    try:
        while True:
            # Wait for messages from the client
            data = await websocket.receive_text()
            # Echo the message back for now
            await websocket.send_json({"message": f"You sent: {data}"})
    except WebSocketDisconnect:
        manager.disconnect(websocket)


# Include routers from other modules
app.include_router(mesh.router, prefix="/mesh", tags=["mesh"])
app.include_router(batch.router, prefix="/batch", tags=["batch"])
app.include_router(insight.router, prefix="/insight", tags=["insight"])


# Make the storage, fusion engine, and clients available to the routes
app.state.storage = storage
app.state.fusion_engine = fusion_engine
app.state.mqtt_client = mqtt_client
app.state.http_client = http_client
app.state.ws_manager = manager


if __name__ == "__main__":
    uvicorn.run("enviromesh.api.main:app", host="0.0.0.0", port=8000, reload=True)
