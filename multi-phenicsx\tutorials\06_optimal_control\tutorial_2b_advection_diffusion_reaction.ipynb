{"cells": [{"cell_type": "markdown", "id": "ab767071", "metadata": {}, "source": ["# Tutorial 06, case 2b: advection diffusion reaction problem with distributed control\n", "\n", "In this tutorial we solve the optimal control problem\n", "\n", "$$\\min J(y, u) = \\frac{1}{2} \\int_{\\Omega_1 \\cup \\Omega_2} (y - y_d)^2 dx + \\frac{\\alpha}{2} \\int_{\\Omega} u^2 dx$$\n", "s.t.\n", "$$\\begin{cases}\n", "- \\epsilon \\Delta y + \\beta \\cdot \\nabla y + \\sigma y = f + u     & \\text{in } \\Omega\\\\\n", "                                                    y = g         & \\text{on } \\partial\\Omega\n", "\\end{cases}$$\n", "\n", "where\n", "$$\\begin{align*}\n", "& \\Omega                     & \\text{domain}\\\\\n", "& u \\in L^2(\\Omega)          & \\text{control variable}\\\\\n", "& y \\in H^1_0(\\Omega)        & \\text{state variable}\\\\\n", "& \\alpha > 0                 & \\text{penalization parameter}\\\\\n", "& y_d                        & \\text{desired state}\\\\\n", "& \\epsilon > 0               & \\text{diffusion coefficient}\\\\\n", "& \\beta \\in \\mathbb{R}^2     & \\text{advection field}\\\\\n", "& \\sigma > 0                 & \\text{reaction coefficient}\\\\\n", "& f                          & \\text{forcing term}\\\\\n", "& g                          & \\text{non homogeneous piecewise constant Di<PERSON>let BC}\\\\\n", "\\end{align*}$$\n", "using an adjoint formulation solved by a one shot approach.\n", "\n", "The test case is from section 5.2 of\n", "```\n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>. Reduced Basis Method for Parametrized Elliptic Optimal Control Problems. SIAM Journal on Scientific Computing, 35(5): A2316-A2340, 2013.\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "26cd3010", "metadata": {}, "outputs": [], "source": ["import typing"]}, {"cell_type": "code", "execution_count": null, "id": "742a5bbe", "metadata": {}, "outputs": [], "source": ["import dolfinx.fem\n", "import dolfinx.fem.petsc\n", "import dolfinx.io\n", "import dolfinx.mesh\n", "import gmsh\n", "import mpi4py.MPI\n", "import numpy as np\n", "import numpy.typing\n", "import petsc4py.PETSc\n", "import ufl\n", "import viskex"]}, {"cell_type": "code", "execution_count": null, "id": "79ecd4d1", "metadata": {}, "outputs": [], "source": ["import multiphenicsx.fem\n", "import multiphenicsx.fem.petsc"]}, {"cell_type": "markdown", "id": "366bcac9", "metadata": {}, "source": ["### Geometrical parameters"]}, {"cell_type": "code", "execution_count": null, "id": "09fd42a9", "metadata": {}, "outputs": [], "source": ["mesh_size = 0.05"]}, {"cell_type": "markdown", "id": "c093075c", "metadata": {}, "source": ["### Mesh"]}, {"cell_type": "code", "execution_count": null, "id": "a0f218f5", "metadata": {}, "outputs": [], "source": ["class GenerateRectangleLines:\n", "    \"\"\"Generate a rectangle.\"\"\"\n", "\n", "    def __init__(self) -> None:\n", "        self.points: dict[tuple[float, float], int] = dict()\n", "        self.lines: dict[tuple[int, int], int] = dict()\n", "\n", "    def add_point(self, x: float, y: float) -> int:\n", "        \"\"\"Add a point to gmsh, if not present already.\"\"\"\n", "        key = (x, y)\n", "        try:\n", "            return self.points[key]\n", "        except KeyError:\n", "            p = gmsh.model.geo.addPoint(x, y, 0.0, mesh_size)\n", "            self.points[key] = p\n", "            return p  # type: ignore[no-any-return]\n", "\n", "    def add_line(self, p0: int, p1: int) -> int:\n", "        \"\"\"Add a line to gmsh, if not present already.\"\"\"\n", "        try:\n", "            return self.lines[(p0, p1)]\n", "        except KeyError:\n", "            l01 = gmsh.model.geo.addLine(p0, p1)\n", "            self.lines[(p0, p1)] = l01\n", "            self.lines[(p1, p0)] = -l01\n", "            return l01  # type: ignore[no-any-return]\n", "\n", "    def __call__(\n", "        self, x_min: float, x_max: float, y_min: float, y_max: typing.Union[float, list[float]]\n", "    ) -> tuple[int, list[int], int, int]:\n", "        \"\"\"Add points and lines that define a rectangle with the provided coordinates.\"\"\"\n", "        p0 = self.add_point(x_min, y_min)\n", "        p1 = self.add_point(x_max, y_min)\n", "        if isinstance(y_max, list):\n", "            p2 = [self.add_point(x_max, y) for y in y_max]\n", "            p3 = self.add_point(x_min, y_max[-1])\n", "        else:\n", "            p2 = [self.add_point(x_max, y_max)]\n", "            p3 = self.add_point(x_min, y_max)\n", "        l0 = self.add_line(p0, p1)\n", "        p1_p2 = [p1, *p2]\n", "        l1 = [self.add_line(p1_p2[i], p1_p2[i + 1]) for i in range(len(p2))]\n", "        l2 = self.add_line(p2[-1], p3)\n", "        l3 = self.add_line(p3, p0)\n", "        return (l0, l1, l2, l3)"]}, {"cell_type": "code", "execution_count": null, "id": "a7f15630", "metadata": {}, "outputs": [], "source": ["gmsh.initialize()\n", "gmsh.model.add(\"mesh\")"]}, {"cell_type": "code", "execution_count": null, "id": "7d2957ce", "metadata": {}, "outputs": [], "source": ["generate_rectangle_lines = GenerateRectangleLines()\n", "[l0, l1, l2, l3] = generate_rectangle_lines(0.0, 1.0, 0.0, 1.0)\n", "[l4, l5, l6, _] = generate_rectangle_lines(1.0, 2.5, 0.0, [0.3, 0.7, 1.0])\n", "[l7, l8, l9, l10] = generate_rectangle_lines(0.2, 0.8, 0.3, 0.7)\n", "[l11, l12, l13, l14] = generate_rectangle_lines(1.2, 2.5, 0.3, 0.7)\n", "line_loop_rectangle_outer_left = gmsh.model.geo.addCurveLoop([l0, l1[0], l2, l3])\n", "line_loop_rectangle_outer_right = gmsh.model.geo.addCurveLoop([l4, l5[0], l5[1], l5[2], l6, -l1[0]])\n", "line_loop_rectangle_inner_left = gmsh.model.geo.addCurveLoop([l7, l8[0], l9, l10])\n", "line_loop_rectangle_inner_right = gmsh.model.geo.addCurveLoop([l11, l12[0], l13, l14])\n", "rectangle_outer_left = gmsh.model.geo.addPlaneSurface(\n", "    [line_loop_rectangle_outer_left, line_loop_rectangle_inner_left])\n", "rectangle_outer_right = gmsh.model.geo.addPlaneSurface(\n", "    [line_loop_rectangle_outer_right, line_loop_rectangle_inner_right])\n", "rectangle_inner_left = gmsh.model.geo.addPlaneSurface([line_loop_rectangle_inner_left])\n", "rectangle_inner_right = gmsh.model.geo.addPlaneSurface([line_loop_rectangle_inner_right])"]}, {"cell_type": "code", "execution_count": null, "id": "49f635e3", "metadata": {}, "outputs": [], "source": ["gmsh.model.geo.synchronize()\n", "gmsh.model.addPhysicalGroup(1, [l0, l2, l3], 1)\n", "gmsh.model.addPhysicalGroup(1, [l4, l6], 2)\n", "gmsh.model.addPhysicalGroup(1, [l5[0], l5[1], l5[2]], 3)\n", "gmsh.model.addPhysicalGroup(2, [rectangle_outer_left], 3)\n", "gmsh.model.addPhysicalGroup(2, [rectangle_outer_right], 4)\n", "gmsh.model.addPhysicalGroup(2, [rectangle_inner_left], 1)\n", "gmsh.model.addPhysicalGroup(2, [rectangle_inner_right], 2)\n", "gmsh.model.mesh.generate(2)"]}, {"cell_type": "code", "execution_count": null, "id": "ec1e2480", "metadata": {}, "outputs": [], "source": ["mesh, subdomains, boundaries, *other_tags = dolfinx.io.gmshio.model_to_mesh(\n", "    gmsh.model, comm=mpi4py.MPI.COMM_WORLD, rank=0, gdim=2)\n", "gmsh.finalize()\n", "assert subdomains is not None\n", "assert boundaries is not None"]}, {"cell_type": "code", "execution_count": null, "id": "2217c7bc-34a8-42d8-87ae-4f0aca553f58", "metadata": {}, "outputs": [], "source": ["# Create connectivities required by the rest of the code\n", "mesh.topology.create_connectivity(mesh.topology.dim - 1, mesh.topology.dim)"]}, {"cell_type": "code", "execution_count": null, "id": "3c46b4ef", "metadata": {}, "outputs": [], "source": ["# Define associated measures\n", "dx = ufl.Measure(\"dx\", subdomain_data=subdomains)"]}, {"cell_type": "code", "execution_count": null, "id": "f2dbb1b4", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_mesh(mesh)"]}, {"cell_type": "code", "execution_count": null, "id": "a9e0cad6", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_mesh_tags(mesh, subdomains, \"subdomains\")"]}, {"cell_type": "code", "execution_count": null, "id": "0edf31ae", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_mesh_tags(mesh, boundaries, \"boundaries\")"]}, {"cell_type": "markdown", "id": "bda24e54", "metadata": {}, "source": ["### Function spaces"]}, {"cell_type": "code", "execution_count": null, "id": "12d46347", "metadata": {}, "outputs": [], "source": ["Y = dolfinx.fem.functionspace(mesh, (\"Lagrange\", 1))\n", "U = dolfinx.fem.functionspace(mesh, (\"Lagrange\", 1))\n", "Q = Y.clone()"]}, {"cell_type": "markdown", "id": "1dce804b", "metadata": {}, "source": ["### Trial and test functions"]}, {"cell_type": "code", "execution_count": null, "id": "4002e468", "metadata": {}, "outputs": [], "source": ["(y, u, p) = (ufl.TrialFunction(Y), ufl.TrialFunction(U), ufl.TrialFunction(Q))\n", "(z, v, q) = (ufl.TestFunction(Y), ufl.TestFunction(U), ufl.TestFunction(Q))"]}, {"cell_type": "markdown", "id": "1f58750e", "metadata": {}, "source": [" ### Problem data"]}, {"cell_type": "code", "execution_count": null, "id": "6680dce3", "metadata": {}, "outputs": [], "source": ["alpha = 0.01\n", "y_d_1 = 0.6\n", "y_d_2 = 1.8\n", "epsilon = 1. / 15.\n", "x = ufl.SpatialCoordinate(mesh)\n", "beta = ufl.as_vector((x[1] * (1 - x[1]), 0))\n", "sigma = dolfinx.fem.Constant(mesh, petsc4py.PETSc.ScalarType(0))\n", "ff = dolfinx.fem.Constant(mesh, petsc4py.PETSc.ScalarType(0))"]}, {"cell_type": "markdown", "id": "2d502202", "metadata": {}, "source": ["### Optimality conditions"]}, {"cell_type": "code", "execution_count": null, "id": "5889e56e", "metadata": {}, "outputs": [], "source": ["state_operator = (epsilon * ufl.inner(ufl.grad(y), ufl.grad(q)) * dx\n", "                  + ufl.inner(ufl.dot(beta, ufl.grad(y)), q) * dx + sigma * ufl.inner(y, q) * dx)\n", "adjoint_operator = (epsilon * ufl.inner(ufl.grad(p), ufl.grad(z)) * dx\n", "                    - ufl.inner(ufl.dot(beta, ufl.grad(p)), z) * dx + sigma * ufl.inner(p, z) * dx)\n", "a = [[ufl.inner(y, z) * (dx(1) + dx(2)), None, adjoint_operator],\n", "     [None, alpha * ufl.inner(u, v) * dx, - ufl.inner(p, v) * dx],\n", "     [state_operator, - ufl.inner(u, q) * dx, None]]\n", "f = [ufl.inner(y_d_1, z) * dx(1) + ufl.inner(y_d_2, z) * dx(2),\n", "     None,\n", "     ufl.inner(ff, q) * dx]\n", "a[0][0] += dolfinx.fem.Constant(mesh, petsc4py.PETSc.ScalarType(0)) * ufl.inner(y, z) * dx\n", "a[2][2] = dolfinx.fem.Constant(mesh, petsc4py.PETSc.ScalarType(0)) * ufl.inner(p, q) * dx\n", "f[1] = ufl.inner(dolfinx.fem.Constant(mesh, petsc4py.PETSc.ScalarType(0)), v) * dx\n", "a_cpp = dolfinx.fem.form(a)\n", "f_cpp = dolfinx.fem.form(f)\n", "\n", "\n", "def bdofs_Y(idx: int) -> np.typing.NDArray[np.int32]:\n", "    \"\"\"Return DOFs of the space Y located on the boundary `idx`.\"\"\"\n", "    assert boundaries is not None\n", "    return dolfinx.fem.locate_dofs_topological(\n", "        Y, mesh.topology.dim - 1, boundaries.indices[boundaries.values == idx])\n", "\n", "\n", "def bdofs_Q(idx: int) -> np.typing.NDArray[np.int32]:\n", "    \"\"\"Return DOFs of the space Q located on the boundary `idx`.\"\"\"\n", "    assert boundaries is not None\n", "    return dolfinx.fem.locate_dofs_topological(\n", "        Q, mesh.topology.dim - 1, boundaries.indices[boundaries.values == idx])\n", "\n", "\n", "bc_state = [dolfinx.fem.dirichletbc(petsc4py.PETSc.ScalarType(idx), bdofs_Y(idx), Y) for idx in (1, 2)]\n", "bc_adjoint = [dolfinx.fem.dirichletbc(petsc4py.PETSc.ScalarType(0), bdofs_Q(idx), Q) for idx in (1, 2)]"]}, {"cell_type": "markdown", "id": "6f7e0064", "metadata": {}, "source": ["### Solution"]}, {"cell_type": "code", "execution_count": null, "id": "16ebc156", "metadata": {}, "outputs": [], "source": ["(y, u, p) = (dolfinx.fem.Function(Y), dolfinx.fem.Function(U), dolfinx.fem.Function(Q))"]}, {"cell_type": "markdown", "id": "2c24debb", "metadata": {}, "source": ["### Cost functional"]}, {"cell_type": "code", "execution_count": null, "id": "2dac82aa", "metadata": {}, "outputs": [], "source": ["J = (0.5 * ufl.inner(y - y_d_1, y - y_d_1) * dx(1) + 0.5 * ufl.inner(y - y_d_2, y - y_d_2) * dx(2)\n", "     + 0.5 * alpha * ufl.inner(u, u) * dx)\n", "J_cpp = dolfinx.fem.form(J)"]}, {"cell_type": "markdown", "id": "bd3173f1", "metadata": {}, "source": ["### Uncontrolled functional value"]}, {"cell_type": "code", "execution_count": null, "id": "244ba85d", "metadata": {}, "outputs": [], "source": ["# Extract state forms from the optimality conditions\n", "a_state = ufl.replace(a[2][0], {q: z})\n", "f_state = ufl.replace(f[2], {q: z})\n", "a_state_cpp = dolfinx.fem.form(a_state)\n", "f_state_cpp = dolfinx.fem.form(f_state)"]}, {"cell_type": "code", "execution_count": null, "id": "63744135", "metadata": {}, "outputs": [], "source": ["# Assemble the linear system for the state\n", "A_state = dolfinx.fem.petsc.assemble_matrix(a_state_cpp, bcs=bc_state)\n", "A_state.assemble()\n", "F_state = dolfinx.fem.petsc.assemble_vector(f_state_cpp)\n", "dolfinx.fem.petsc.apply_lifting(F_state, [a_state_cpp], [bc_state])\n", "F_state.ghostUpdate(addv=petsc4py.PETSc.InsertMode.ADD, mode=petsc4py.PETSc.ScatterMode.REVERSE)\n", "dolfinx.fem.petsc.set_bc(F_state, bc_state)"]}, {"cell_type": "code", "execution_count": null, "id": "f890c33c", "metadata": {}, "outputs": [], "source": ["# Solve\n", "ksp = petsc4py.PETSc.KSP()\n", "ksp.create(mesh.comm)\n", "ksp.setOperators(A_state)\n", "ksp.setType(\"preonly\")\n", "ksp.getPC().setType(\"lu\")\n", "ksp.getPC().setFactorSolverType(\"mumps\")\n", "ksp.setFromOptions()\n", "ksp.solve(F_state, y.x.petsc_vec)\n", "y.x.petsc_vec.ghostUpdate(addv=petsc4py.PETSc.InsertMode.INSERT, mode=petsc4py.PETSc.ScatterMode.FORWARD)\n", "ksp.destroy()"]}, {"cell_type": "code", "execution_count": null, "id": "7307e446", "metadata": {}, "outputs": [], "source": ["J_uncontrolled = mesh.comm.allreduce(dolfinx.fem.assemble_scalar(J_cpp), op=mpi4py.MPI.SUM)\n", "print(\"Uncontrolled J =\", J_uncontrolled)\n", "assert np.isclose(J_uncontrolled, 0.028096831)"]}, {"cell_type": "code", "execution_count": null, "id": "496520be", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(y, \"uncontrolled state\")"]}, {"cell_type": "markdown", "id": "dab4e8d3", "metadata": {}, "source": ["### Optimal control"]}, {"cell_type": "code", "execution_count": null, "id": "2cd0a9f4", "metadata": {}, "outputs": [], "source": ["# Assemble the block linear system for the optimality conditions\n", "bc = bc_state + bc_adjoint\n", "A = dolfinx.fem.petsc.assemble_matrix_block(a_cpp, bcs=bc)\n", "A.assemble()\n", "F = dolfinx.fem.petsc.assemble_vector_block(f_cpp, a_cpp, bcs=bc)"]}, {"cell_type": "code", "execution_count": null, "id": "008df675", "metadata": {}, "outputs": [], "source": ["# Solve\n", "yup = dolfinx.fem.petsc.create_vector_block(f_cpp)\n", "ksp = petsc4py.PETSc.KSP()\n", "ksp.create(mesh.comm)\n", "ksp.setOperators(A)\n", "ksp.setType(\"preonly\")\n", "ksp.getPC().setType(\"lu\")\n", "ksp.getPC().setFactorSolverType(\"mumps\")\n", "ksp.setFromOptions()\n", "ksp.solve(F, yup)\n", "yup.ghostUpdate(addv=petsc4py.PETSc.InsertMode.INSERT, mode=petsc4py.PETSc.ScatterMode.FORWARD)\n", "ksp.destroy()"]}, {"cell_type": "code", "execution_count": null, "id": "434b234c", "metadata": {}, "outputs": [], "source": ["# Split the block solution in components\n", "with multiphenicsx.fem.petsc.BlockVecSubVectorWrapper(yup, [<PERSON><PERSON>dofmap, <PERSON><PERSON>dofmap, <PERSON>.dofmap]) as yup_wrapper:\n", "    for yup_wrapper_local, component in zip(yup_wrapper, (y, u, p)):\n", "        with component.x.petsc_vec.localForm() as component_local:\n", "            component_local[:] = yup_wrapper_local"]}, {"cell_type": "code", "execution_count": null, "id": "d304b121", "metadata": {}, "outputs": [], "source": ["J_controlled = mesh.comm.allreduce(dolfinx.fem.assemble_scalar(J_cpp), op=mpi4py.MPI.SUM)\n", "print(\"Optimal J =\", J_controlled)\n", "assert np.isclose(J_controlled, 0.001775304)"]}, {"cell_type": "code", "execution_count": null, "id": "88b5009e", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(y, \"state\")"]}, {"cell_type": "code", "execution_count": null, "id": "78a79e5b", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(u, \"control\")"]}, {"cell_type": "code", "execution_count": null, "id": "254d35a6", "metadata": {}, "outputs": [], "source": ["viskex.dolfinx.plot_scalar_field(p, \"adjoint\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython"}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python"}}, "nbformat": 4, "nbformat_minor": 5}