name: multiphenicsx CI

on:
  push:
    branches:
      - "**"
  pull_request:
    branches:
      - main
  schedule:
    - cron: "0 3 * * *"
  workflow_call:
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    container: ghcr.io/fenics/dolfinx/dolfinx:nightly
    strategy:
      matrix:
        petsc_arch: [real, complex]
      fail-fast: false
    steps:
      - uses: actions/checkout@v4
      - name: Setup container
        run: |
          . /usr/local/bin/dolfinx-${{ matrix.petsc_arch }}-mode
          echo "PETSC_ARCH=$PETSC_ARCH" >> $GITHUB_ENV
          echo "LD_LIBRARY_PATH=$LD_LIBRARY_PATH" >> $GITHUB_ENV
          echo "PYTHONPATH=$PYTHONPATH" >> $GITHUB_ENV
          echo "PKG_CONFIG_PATH=$PKG_CONFIG_PATH" >> $GITHUB_ENV
          echo "CMAKE_PREFIX_PATH=$CMAKE_PREFIX_PATH" >> $GITHUB_ENV
          rm /dolfinx-env/lib/python3.*/site-packages/petsc4py/py.typed
      - name: Install multiphenicsx
        run: |
          python3 -m pip install --check-build-dependencies --no-build-isolation --config-settings=build-dir="build" --config-settings=cmake.build-type="Debug" --verbose .[docs,lint,tests,tutorials]
      - name: Clean build files
        run: |
          git config --global --add safe.directory $PWD
          git clean -xdf
      - name: Run ruff on python files
        run: |
          python3 -m ruff check .
      - name: Run isort on python files
        run: |
          python3 -m isort --check --diff .
      - name: Run mypy on python files
        run: |
          python3 -m mypy --exclude=conftest.py .
          python3 -m mypy tests/unit/conftest.py
          python3 -m mypy tutorials/conftest.py
      - name: Run yamllint on workflows
        run: |
          python3 -m yamllint -d "{extends: default, rules: {document-start: {present: false}, line-length: disable, truthy: {check-keys: false}}}" .
      - name: Run clang-format on C++ files
        run: |
          wget https://raw.githubusercontent.com/FEniCS/dolfinx/main/.clang-format
          find . -type f \( -name "*.cpp" -o -name "*.h" \) | xargs clang-format --dry-run --Werror
      - name: Run cmake-format on cmake files
        run: |
          wget https://raw.githubusercontent.com/FEniCS/dolfinx/main/.cmake-format
          find . -type f \( -name "*.cmake" -o -name "*.cmake.in" -o -name "CMakeLists.txt" \) | xargs cmake-format --check
      - name: Run documentation generation
        run: |
          cd docs && python3 -m sphinx -W -b html . build/html
      - name: Remove source directory to ensure that package from installation directory is used
        run: |
          rm -rf multiphenicsx
      - name: Run unit tests (serial)
        run: |
          COVERAGE_FILE=.coverage_unit_serial python3 -m coverage run --source=multiphenicsx -m pytest tests/unit
      - name: Run unit tests (parallel)
        run: |
          COVERAGE_FILE=.coverage_unit_parallel mpirun -n 3 python3 -m coverage run --source=multiphenicsx --parallel-mode -m pytest tests/unit
      - name: Combine coverage reports
        run: |
          python3 -m coverage combine .coverage*
          python3 -m coverage report --fail-under=100 --show-missing --skip-covered
      - name: Generate tutorial files
        run: |
          NO_TESTS_COLLECTED=5
          python3 -m pytest --ipynb-action=create-notebooks tutorials || (($?==$NO_TESTS_COLLECTED))
          python3 -m pytest --ipynb-action=create-notebooks --np=2 tutorials || (($?==$NO_TESTS_COLLECTED))
        shell: bash
      - name: Run ruff on tutorial files
        run: |
          python3 -m nbqa ruff .
      - name: Run isort on tutorial files
        run: |
          python3 -m nbqa isort --check --diff .
      - name: Run mypy on tutorial files
        run: |
          python3 -m nbqa mypy .
      - name: Check for stray outputs, counts and metadata in tutorial files
        uses: RBniCS/check-jupyter-metadata-action@main
        with:
          pattern: "tutorials/**/*.ipynb"
      - name: Run tutorials (serial)
        run: |
          python3 -m pytest tutorials
      - name: Run tutorials (parallel)
        run: |
          python3 -m pytest --np 3 tutorials
      - name: Upload tutorials logs as an artifact in case of failure
        if: failure() || cancelled()
        uses: actions/upload-artifact@v4
        with:
          name: "tutorials-logs-${{ matrix.petsc_arch }}"
          path: |
            tutorials/**/.ipynb_pytest/**/*.log*
          include-hidden-files: true

  warn:
    runs-on: ubuntu-latest
    if: github.repository == 'multiphenics/multiphenicsx' && github.ref == 'refs/heads/main' && github.event_name == 'schedule'
    steps:
      - name: Warn if scheduled workflow is about to be disabled
        uses: fem-on-colab/warn-workflow-about-to-be-disabled-action@main
        with:
          workflow-filename: ci.yml
          days-elapsed: 55
