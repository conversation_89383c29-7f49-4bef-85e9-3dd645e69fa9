"""API routes for AI-generated insights."""

import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from openai import AsyncOpenAI
from pydantic import BaseModel

from enviromesh.core.models import (
    Insight,
    InsightRequest,
    SensorReading,
    SensorType,
)
from enviromesh.core.storage import TimeSeriesStorage

router = APIRouter()
logger = logging.getLogger(__name__)

# Initialize OpenAI client
openai_api_key = os.environ.get("OPENAI_API_KEY")
openai_client = AsyncOpenAI(api_key=openai_api_key) if openai_api_key else None


def get_storage(request: Request) -> TimeSeriesStorage:
    """Get the storage from the request state.

    Args:
        request: The request object.

    Returns:
        The storage instance.
    """
    return request.app.state.storage


@router.post("/", response_model=Insight)
async def generate_insight(
    request_data: InsightRequest,
    request: Request,
    storage: TimeSeriesStorage = Depends(get_storage),
) -> Insight:
    """Generate an AI insight about mesh data.

    Args:
        request_data: The insight request parameters.
        request: The request object.
        storage: The storage instance.

    Returns:
        An Insight object containing the AI-generated insight.

    Raises:
        HTTPException: If the OpenAI API key is not configured or the API call fails.
    """
    if not openai_client:
        raise HTTPException(
            status_code=503,
            detail="OpenAI API key not configured. Set the OPENAI_API_KEY environment variable.",
        )

    # Parse time range
    start_time = None
    end_time = datetime.utcnow()

    if request_data.time_range:
        if request_data.time_range == "1h":
            start_time = end_time - timedelta(hours=1)
        elif request_data.time_range == "6h":
            start_time = end_time - timedelta(hours=6)
        elif request_data.time_range == "24h":
            start_time = end_time - timedelta(hours=24)
        elif request_data.time_range == "7d":
            start_time = end_time - timedelta(days=7)
        elif request_data.time_range == "30d":
            start_time = end_time - timedelta(days=30)
        else:
            # Default to 24 hours
            start_time = end_time - timedelta(hours=24)
    else:
        # Default to 24 hours
        start_time = end_time - timedelta(hours=24)

    # Get sensor readings
    sensor_readings = await storage.get_sensor_readings(
        sensor_types=request_data.focus,
        start_time=start_time,
        end_time=end_time,
        limit=100,  # Limit to 100 most recent readings
    )

    # Get fusion results
    fusion_results = await storage.get_fusion_results(
        start_time=start_time,
        end_time=end_time,
        limit=100,  # Limit to 100 most recent results
    )

    # Prepare data for the AI
    sensor_data = []
    for reading in sensor_readings:
        sensor_data.append(
            {
                "sensor_id": reading.sensor_id,
                "sensor_type": reading.sensor_type,
                "timestamp": reading.timestamp.isoformat(),
                "value": reading.value,
                "unit": reading.unit,
            }
        )

    fusion_data = []
    for result in fusion_results:
        fusion_data.append(
            {
                "rule_name": result.rule_name,
                "timestamp": result.timestamp.isoformat(),
                "value": result.value,
            }
        )

    # Prepare the prompt for the AI
    prompt = f"""
    You are an environmental data analyst. Analyze the following sensor data and fusion results from the EnviroMesh network.
    
    Mesh ID: {request_data.mesh_id}
    Time Range: {request_data.time_range or '24h'}
    Focus: {', '.join([t.value for t in request_data.focus]) if request_data.focus else 'All sensors'}
    
    Sensor Readings:
    {sensor_data}
    
    Fusion Results:
    {fusion_data}
    
    """

    if request_data.question:
        prompt += f"\nQuestion: {request_data.question}\n"

    prompt += """
    Please provide a concise analysis of the data, including:
    1. Key trends and patterns
    2. Anomalies or unusual events
    3. Correlations between different sensor types
    4. Recommendations or actionable insights
    
    Keep your response under 300 words and focus on the most important insights.
    """

    try:
        # Call the OpenAI API
        response = await openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are an environmental data analyst specializing in sensor data interpretation."},
                {"role": "user", "content": prompt},
            ],
            max_tokens=500,
            temperature=0.7,
        )

        # Extract the insight text
        insight_text = response.choices[0].message.content

        # Create and return the insight
        return Insight(
            mesh_id=request_data.mesh_id,
            timestamp=datetime.utcnow(),
            text=insight_text,
            data_points=sensor_readings + fusion_results,  # Combine both types of data
            confidence=0.85,  # Arbitrary confidence value
            metadata={
                "time_range": request_data.time_range or "24h",
                "focus": [t.value for t in request_data.focus] if request_data.focus else None,
                "question": request_data.question,
                "model": "gpt-4o-mini",
                "token_count": response.usage.total_tokens,
            },
        )

    except Exception as e:
        logger.error(f"Error calling OpenAI API: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating insight: {str(e)}",
        )
