@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Leaflet CSS */
@import 'leaflet/dist/leaflet.css';

@layer base {
  html {
    @apply h-full bg-gray-50;
  }
  body {
    @apply h-full;
  }
  #root {
    @apply h-full;
  }
}

@layer components {
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .btn {
    @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }

  .btn-outline {
    @apply btn border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
  }

  .input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }

  .label {
    @apply block text-sm font-medium text-gray-700;
  }
}
