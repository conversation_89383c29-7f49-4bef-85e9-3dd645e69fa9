{"cells": [{"cell_type": "markdown", "id": "25f73008", "metadata": {}, "source": ["# Tutorial 07: understanding restrictions"]}, {"cell_type": "code", "execution_count": null, "id": "91b93995", "metadata": {}, "outputs": [], "source": ["import os\n", "import typing"]}, {"cell_type": "code", "execution_count": null, "id": "c74ba7d8", "metadata": {}, "outputs": [], "source": ["import basix\n", "import basix.ufl\n", "import dolfinx.fem\n", "import dolfinx.io\n", "import dolfinx.mesh\n", "import gmsh\n", "import matplotlib as mpl\n", "import matplotlib.collections\n", "import matplotlib.pyplot as plt\n", "import matplotlib.tri as tri\n", "import mpi4py.MPI\n", "import mpl_toolkits.axes_grid1\n", "import numpy as np\n", "import numpy.typing"]}, {"cell_type": "code", "execution_count": null, "id": "80f275ee", "metadata": {}, "outputs": [], "source": ["import multiphenicsx.fem"]}, {"cell_type": "markdown", "id": "c8642910", "metadata": {}, "source": ["### Auxiliary functions for plotting"]}, {"cell_type": "code", "execution_count": null, "id": "9d625b8b", "metadata": {}, "outputs": [], "source": ["def plot_mesh(mesh: dolfinx.mesh.Mesh, ax: typing.Optional[plt.Axes] = None) -> plt.Axes:\n", "    \"\"\"Plot a mesh object on the provied (or, if None, the current) axes object.\"\"\"\n", "    if ax is None:\n", "        ax = plt.gca()\n", "    ax.set_aspect(\"equal\")\n", "    points = mesh.geometry.x\n", "    cells = mesh.geometry.dofmap\n", "    tria = tri.Triangulation(points[:, 0], points[:, 1], cells)\n", "    ax.triplot(tria, color=\"k\")\n", "    return ax"]}, {"cell_type": "code", "execution_count": null, "id": "27df1037", "metadata": {}, "outputs": [], "source": ["def plot_mesh_tags(\n", "    mesh: dolfinx.mesh.Mesh, mesh_tags: dolfinx.mesh.MeshTags, ax: typing.Optional[plt.Axes] = None\n", ") -> plt.Axes:\n", "    \"\"\"Plot a mesh tags object on the provied (or, if None, the current) axes object.\"\"\"\n", "    if ax is None:\n", "        ax = plt.gca()\n", "    ax.set_aspect(\"equal\")\n", "    points = mesh.geometry.x\n", "    colors = [\"b\", \"r\"]\n", "    cmap = mpl.colors.ListedColormap(colors)\n", "    cmap_bounds = [0, 0.5, 1]\n", "    norm = mpl.colors.BoundaryNorm(cmap_bounds, cmap.N)\n", "    assert mesh_tags.dim in (mesh.topology.dim, mesh.topology.dim - 1)\n", "    if mesh_tags.dim == mesh.topology.dim:\n", "        cells = mesh.geometry.dofmap\n", "        tria = tri.Triangulation(points[:, 0], points[:, 1], cells)\n", "        cell_colors = np.zeros((cells.shape[0], ))\n", "        cell_colors[mesh_tags.indices[mesh_tags.values == 1]] = 1\n", "        mappable: mpl.collections.Collection = ax.tripcolor(\n", "            tria, cell_colors, edgecolor=\"k\", cmap=cmap, norm=norm)\n", "    elif mesh_tags.dim == mesh.topology.dim - 1:\n", "        tdim = mesh.topology.dim\n", "        cells_map = mesh.topology.index_map(mesh.topology.dim)\n", "        num_cells = cells_map.size_local + cells_map.num_ghosts\n", "        connectivity_cells_to_facets = mesh.topology.connectivity(tdim, tdim - 1)\n", "        connectivity_cells_to_vertices = mesh.topology.connectivity(tdim, 0)\n", "        connectivity_facets_to_vertices = mesh.topology.connectivity(tdim - 1, 0)\n", "        vertex_map = {\n", "            topology_index: geometry_index for c in range(num_cells) for (topology_index, geometry_index) in zip(\n", "                connectivity_cells_to_vertices.links(c), mesh.geometry.dofmap[c])\n", "        }\n", "        linestyles = [(0, (5, 10)), \"solid\"]\n", "        lines = list()\n", "        lines_colors_as_int = list()\n", "        lines_colors_as_str = list()\n", "        lines_linestyles = list()\n", "        mesh_tags_1 = mesh_tags.indices[mesh_tags.values == 1]\n", "        for c in range(num_cells):\n", "            facets = connectivity_cells_to_facets.links(c)\n", "            for f in facets:\n", "                if f in mesh_tags_1:\n", "                    value_f = 1\n", "                else:\n", "                    value_f = 0\n", "                vertices = [vertex_map[v] for v in connectivity_facets_to_vertices.links(f)]\n", "                lines.append(points[vertices][:, :2])\n", "                lines_colors_as_int.append(value_f)\n", "                lines_colors_as_str.append(colors[value_f])\n", "                lines_linestyles.append(linestyles[value_f])\n", "        mappable: mpl.collections.Collection = mpl.collections.LineCollection(  # type: ignore[no-redef]\n", "            lines, cmap=cmap, norm=norm, colors=lines_colors_as_str, linestyles=lines_linestyles)\n", "        mappable.set_array(np.array(lines_colors_as_int))\n", "        ax.add_collection(mappable)\n", "        ax.autoscale()\n", "    divider = mpl_toolkits.axes_grid1.make_axes_locatable(ax)\n", "    cax = divider.append_axes(\"right\", size=\"5%\", pad=0.05)\n", "    plt.colorbar(mappable, cax=cax, boundaries=cmap_bounds, ticks=cmap_bounds)\n", "    return ax"]}, {"cell_type": "code", "execution_count": null, "id": "dcb01983", "metadata": {}, "outputs": [], "source": ["def _plot_dofmap(coordinates: np.typing.NDArray[np.float64], ax: typing.Optional[plt.Axes] = None) -> plt.Axes:\n", "    if ax is None:\n", "        ax = plt.gca()\n", "    text_offset = [1e-2, 1e-2]\n", "    ax.scatter(coordinates[:, 0], coordinates[:, 1], c=\"k\", s=50)\n", "    for c in np.unique(coordinates, axis=0):\n", "        dofs_c = (coordinates == c).all(axis=1).nonzero()[0]\n", "        text_c = np.array2string(dofs_c, separator=\", \", max_line_width=10)\n", "        ax.text(c[0] + text_offset[0], c[1] + text_offset[1], text_c, fontsize=20)\n", "    return ax"]}, {"cell_type": "code", "execution_count": null, "id": "cc2b43f4", "metadata": {}, "outputs": [], "source": ["def plot_dofmap(V: dolfinx.fem.FunctionSpace, ax: typing.Optional[plt.Axes] = None) -> plt.Axes:\n", "    \"\"\"Plot the DOFs in a function space object on the provied (or, if None, the current) axes object.\"\"\"\n", "    coordinates = V.tabulate_dof_coordinates().round(decimals=3)\n", "    return _plot_dofmap(coordinates, ax)"]}, {"cell_type": "code", "execution_count": null, "id": "5c8a94d9", "metadata": {}, "outputs": [], "source": ["def plot_dofmap_restriction(\n", "    V: dolfinx.fem.FunctionSpace, restriction: multiphenicsx.fem.DofMapRestriction,\n", "    ax: typing.Optional[plt.Axes] = None\n", ") -> plt.Axes:\n", "    \"\"\"Plot the DOFs in a DofMapRestriction object on the provied (or, if None, the current) axes object.\"\"\"\n", "    coordinates = V.tabulate_dof_coordinates().round(decimals=3)\n", "    return _plot_dofmap(coordinates[list(restriction.unrestricted_to_restricted.keys())], ax)"]}, {"cell_type": "markdown", "id": "afab0a88", "metadata": {}, "source": ["### Auxiliary functions for asserts"]}, {"cell_type": "code", "execution_count": null, "id": "d977af7a", "metadata": {}, "outputs": [], "source": ["def count_dofs(restriction: multiphenicsx.fem.DofMapRestriction, comm: mpi4py.MPI.Intracomm) -> int:\n", "    \"\"\"Count the DOFs in a DofMapRestriction object.\"\"\"\n", "    u2r = restriction.unrestricted_to_restricted\n", "    restricted_local_indices = np.array([r for (_, r) in u2r.items()], dtype=np.int32)\n", "    dofs_V_restriction_global = restriction.index_map.local_to_global(restricted_local_indices)\n", "    return len(set(gdof for gdofs in comm.allgather(dofs_V_restriction_global) for gdof in gdofs))"]}, {"cell_type": "code", "execution_count": null, "id": "ec4aec83", "metadata": {}, "outputs": [], "source": ["def locate_dofs_by_polar_coordinates(\n", "    r: typing.Union[int, float, np.float64], theta: typing.Union[int, float, np.float64],\n", "    V: dolfinx.fem.FunctionSpace, restriction: multiphenicsx.fem.DofMapRestriction\n", ") -> set[np.int32]:\n", "    \"\"\"Determine which DOFs in a DofMapRestriction object are located at a point (written in polar coordinates).\"\"\"\n", "    p = [r * np.cos(theta), r * np.sin(theta), 0.]\n", "    dofs_V = dolfinx.fem.locate_dofs_geometrical(V, lambda x: np.isclose(x.T, p).all(axis=1)).reshape(-1)\n", "    u2r = restriction.unrestricted_to_restricted\n", "    restricted_local_indices = np.array([u2r[dof] for dof in dofs_V if dof in u2r], dtype=np.int32)\n", "    dofs_V_restriction_global = restriction.index_map.local_to_global(restricted_local_indices)\n", "    return set(gdof for gdofs in V.mesh.comm.allgather(dofs_V_restriction_global) for gdof in gdofs)"]}, {"cell_type": "markdown", "id": "a507ec54", "metadata": {}, "source": ["### Geometrical parameters"]}, {"cell_type": "code", "execution_count": null, "id": "55a65ad1", "metadata": {}, "outputs": [], "source": ["r = 1\n", "mesh_size = 2"]}, {"cell_type": "markdown", "id": "b1d4f038", "metadata": {}, "source": ["### Mesh\n", "\n", "Generate a simple mesh consisting in an hexagon discretized with six equilateral triangle cells."]}, {"cell_type": "code", "execution_count": null, "id": "bb2e79de", "metadata": {}, "outputs": [], "source": ["gmsh.initialize()\n", "gmsh.model.add(\"mesh\")"]}, {"cell_type": "code", "execution_count": null, "id": "5a2c3dd6", "metadata": {}, "outputs": [], "source": ["points = [\n", "    gmsh.model.geo.addPoint(np.cos(t / 3 * np.pi), np.sin(t / 3 * np.pi), 0.0, mesh_size) for t in range(6)]\n", "lines = [gmsh.model.geo.addLine(points[t], points[(t + 1) % 6]) for t in range(6)]\n", "line_loop = gmsh.model.geo.addCurveLoop(lines)\n", "domain = gmsh.model.geo.addPlaneSurface([line_loop])"]}, {"cell_type": "code", "execution_count": null, "id": "daa65c8d", "metadata": {}, "outputs": [], "source": ["gmsh.model.geo.synchronize()\n", "gmsh.model.addPhysicalGroup(1, lines, 0)\n", "gmsh.model.addPhysicalGroup(2, [domain], 0)\n", "gmsh.model.mesh.generate(2)"]}, {"cell_type": "code", "execution_count": null, "id": "18bcf5ac", "metadata": {}, "outputs": [], "source": ["mesh, subdomains, boundaries, *other_tags = dolfinx.io.gmshio.model_to_mesh(\n", "    gmsh.model, comm=mpi4py.MPI.COMM_WORLD, rank=0, gdim=2)\n", "gmsh.finalize()\n", "assert subdomains is not None\n", "assert boundaries is not None"]}, {"cell_type": "code", "execution_count": null, "id": "d389b666", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    plot_mesh(mesh)"]}, {"cell_type": "markdown", "id": "ba326808", "metadata": {}, "source": ["Create connectivities required by the rest of the code."]}, {"cell_type": "code", "execution_count": null, "id": "c7ca57ae", "metadata": {}, "outputs": [], "source": ["mesh.topology.create_connectivity(mesh.topology.dim - 1, 0)\n", "mesh.topology.create_connectivity(mesh.topology.dim - 1, mesh.topology.dim)\n", "mesh.topology.create_connectivity(mesh.topology.dim, 0)\n", "mesh.topology.create_connectivity(mesh.topology.dim, mesh.topology.dim - 1)\n", "mesh.topology.create_connectivity(mesh.topology.dim, mesh.topology.dim)"]}, {"cell_type": "markdown", "id": "846e7d8a", "metadata": {}, "source": ["### Mesh restrictions on cells\n", "\n", "Define mesh tags on cells, which are equal to one on all cells."]}, {"cell_type": "code", "execution_count": null, "id": "05103451", "metadata": {}, "outputs": [], "source": ["cell_entities_all = dolfinx.mesh.locate_entities(\n", "    mesh, mesh.topology.dim, lambda x: np.full((x.shape[1], ), True))\n", "cell_values_all = np.full(cell_entities_all.shape, 1, dtype=np.int32)\n", "cell_restriction_all = dolfinx.mesh.meshtags(\n", "    mesh, mesh.topology.dim, cell_entities_all, cell_values_all)\n", "cell_restriction_all.name = \"cell_restriction_all\""]}, {"cell_type": "code", "execution_count": null, "id": "b6acc651", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    plot_mesh_tags(mesh, cell_restriction_all)"]}, {"cell_type": "markdown", "id": "190dc01b", "metadata": {}, "source": ["Define mesh tags on cells, which are equal to one on one half of the cells"]}, {"cell_type": "code", "execution_count": null, "id": "dcab9b8f", "metadata": {}, "outputs": [], "source": ["eps = np.finfo(float).eps\n", "cell_entities_subset = dolfinx.mesh.locate_entities(\n", "    mesh, mesh.topology.dim,\n", "    lambda x: np.logical_or(x[0] < eps, np.logical_and(x[1] < eps, x[0] < 0.5 + eps)))\n", "cell_values_subset = np.full(cell_entities_subset.shape, 1, dtype=np.int32)\n", "cell_restriction_subset = dolfinx.mesh.meshtags(\n", "    mesh, mesh.topology.dim, cell_entities_subset, cell_values_subset)\n", "cell_restriction_subset.name = \"cell_restriction_subset\""]}, {"cell_type": "code", "execution_count": null, "id": "01b70521", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    plot_mesh_tags(mesh, cell_restriction_subset)"]}, {"cell_type": "markdown", "id": "c803c250", "metadata": {}, "source": ["### Mesh restrictions on facets\n", "\n", "Define mesh tags on facets, which are equal to one on all facets"]}, {"cell_type": "code", "execution_count": null, "id": "4c99c980", "metadata": {}, "outputs": [], "source": ["facet_entities_all = dolfinx.mesh.locate_entities(\n", "    mesh, mesh.topology.dim - 1, lambda x: np.full((x.shape[1], ), True))\n", "facet_values_all = np.full(facet_entities_all.shape, 1, dtype=np.int32)\n", "facet_restriction_all = dolfinx.mesh.meshtags(\n", "    mesh, mesh.topology.dim - 1, facet_entities_all, facet_values_all)\n", "facet_restriction_all.name = \"facet_restriction_all\""]}, {"cell_type": "code", "execution_count": null, "id": "fa4fa38d", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    plot_mesh_tags(mesh, facet_restriction_all)"]}, {"cell_type": "markdown", "id": "ffd2f693", "metadata": {}, "source": ["Define mesh tags on facets, which are equal to one on two facets"]}, {"cell_type": "code", "execution_count": null, "id": "e24ada55", "metadata": {}, "outputs": [], "source": ["facet_entities_subset = dolfinx.mesh.locate_entities(\n", "    mesh, mesh.topology.dim - 1, lambda x: np.fabs(x[1] + np.sqrt(3) * x[0]) < 0.01)\n", "facet_values_subset = np.full(facet_entities_subset.shape, 1, dtype=np.int32)\n", "facet_restriction_subset = dolfinx.mesh.meshtags(\n", "    mesh, mesh.topology.dim - 1, facet_entities_subset, facet_values_subset)\n", "facet_restriction_subset.name = \"facet_restriction_subset\""]}, {"cell_type": "code", "execution_count": null, "id": "4f7e2628", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    plot_mesh_tags(mesh, facet_restriction_subset)"]}, {"cell_type": "markdown", "id": "61d9a8cb", "metadata": {}, "source": ["### Mesh restrictions summary"]}, {"cell_type": "code", "execution_count": null, "id": "e249ce10", "metadata": {}, "outputs": [], "source": ["cell_restrictions = (cell_restriction_all, cell_restriction_subset)\n", "facet_restrictions = (facet_restriction_all, facet_restriction_subset)\n", "all_restrictions = cell_restrictions + facet_restrictions"]}, {"cell_type": "markdown", "id": "07b02caa", "metadata": {}, "source": ["### Lagrange spaces\n", "\n", "Define Lagrange FE spaces of order $k=1, 2, 3$, and plot the associated DofMap."]}, {"cell_type": "code", "execution_count": null, "id": "29e23944", "metadata": {}, "outputs": [], "source": ["CG_elem = [\n", "    basix.ufl.element(\n", "        \"Lagrange\", mesh.basix_cell(), k, lagrange_variant=basix.LagrangeVariant.equispaced\n", "    ) for k in (1, 2, 3)\n", "]\n", "CG = [dolfinx.fem.functionspace(mesh, CG_elem_k) for CG_elem_k in CG_elem]"]}, {"cell_type": "code", "execution_count": null, "id": "d1040c88", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    _, ax = plt.subplots(3, 1, figsize=(10, 30))\n", "    assert isinstance(ax, np.ndarray)\n", "    for (k, CGk) in enumerate(CG):\n", "        plot_mesh(mesh, ax[k])\n", "        plot_dofmap(CGk, ax[k])\n", "        ax[k].set_title(\"CG \" + str(k + 1) + \" DofMap\", fontsize=30)"]}, {"cell_type": "markdown", "id": "a221f99f", "metadata": {}, "source": ["Define DofMapRestriction objects associated to the Lagrange FE spaces, for all four restrictions"]}, {"cell_type": "code", "execution_count": null, "id": "36d19c07", "metadata": {}, "outputs": [], "source": ["dofmap_restriction_CG: dict[\n", "    dolfinx.mesh.MeshTags, list[multiphenicsx.fem.DofMapRestriction]] = dict()\n", "for restriction in all_restrictions:\n", "    dofmap_restriction_CG[restriction] = list()\n", "    for CGk in CG:\n", "        restrict_CGk = dolfinx.fem.locate_dofs_topological(\n", "            CGk, restriction.dim, restriction.indices[restriction.values == 1])\n", "        dofmap_restriction_CG[restriction].append(\n", "            multiphenicsx.fem.DofMapRestriction(CGk.dofmap, restrict_CGk))"]}, {"cell_type": "markdown", "id": "531f1128", "metadata": {}, "source": ["Compare DOFs for the case of cell restriction equal to one on the entire domain. There is indeed no difference."]}, {"cell_type": "code", "execution_count": null, "id": "2fbda32b", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    _, ax = plt.subplots(3, 2, figsize=(20, 30))\n", "    assert isinstance(ax, np.ndarray)\n", "    for (k, CGk) in enumerate(CG):\n", "        plot_mesh(mesh, ax[k, 0])\n", "        plot_dofmap(CGk, ax[k, 0])\n", "        ax[k, 0].set_title(\"CG \" + str(k + 1) + \" DofMap\", fontsize=30)\n", "    for (k, (CGk, dofmap_restriction_CGk)) in enumerate(zip(CG, dofmap_restriction_CG[cell_restriction_all])):\n", "        plot_mesh_tags(mesh, cell_restriction_all, ax[k, 1])\n", "        plot_dofmap_restriction(CGk, dofmap_restriction_CGk, ax[k, 1])\n", "        ax[k, 1].set_title(\"CG \" + str(k + 1) + \" DofMapRestriction\", fontsize=30)"]}, {"cell_type": "markdown", "id": "16cf8167", "metadata": {}, "source": ["Assert that DOFs are at the expected locations"]}, {"cell_type": "code", "execution_count": null, "id": "bae4835c", "metadata": {}, "outputs": [], "source": ["CG_1 = CG[0]\n", "dofmap_restriction_CG_1 = dofmap_restriction_CG[cell_restriction_all][0]\n", "assert count_dofs(dofmap_restriction_CG_1, mesh.comm) == 7\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 0, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "ca7ecd6f", "metadata": {}, "outputs": [], "source": ["CG_2 = CG[1]\n", "dofmap_restriction_CG_2 = dofmap_restriction_CG[cell_restriction_all][1]\n", "assert count_dofs(dofmap_restriction_CG_2, mesh.comm) == 19\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 0, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 2 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, np.pi, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 4 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 5 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 0, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 3 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 5 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 7 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 9 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 11 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "f0a776ba", "metadata": {}, "outputs": [], "source": ["CG_3 = CG[2]\n", "dofmap_restriction_CG_3 = dofmap_restriction_CG[cell_restriction_all][2]\n", "assert count_dofs(dofmap_restriction_CG_3, mesh.comm) == 37\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 0, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 2 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, np.pi, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 4 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 5 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 0, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 2 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, np.pi, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 4 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 5 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 0, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, np.pi / 6, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 3 * np.pi / 6, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 5 * np.pi / 6, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 7 * np.pi / 6, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 9 * np.pi / 6, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 11 * np.pi / 6, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.pi / 3 - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.pi / 3 + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 2 * np.pi / 3 - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 2 * np.pi / 3 + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.pi - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.pi + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 4 * np.pi / 3 - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 4 * np.pi / 3 + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 5 * np.pi / 3 - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 5 * np.pi / 3 + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.pi - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1"]}, {"cell_type": "markdown", "id": "66cccc58", "metadata": {}, "source": ["Compare DOFs for che case of cell restriction equal to one on a subset of the domain. Note how the DofMapRestriction has only a subset of the DOFs of the DofMap, and properly renumbers them."]}, {"cell_type": "code", "execution_count": null, "id": "d3a68c0f", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    _, ax = plt.subplots(3, 2, figsize=(20, 30))\n", "    assert isinstance(ax, np.ndarray)\n", "    for (k, CGk) in enumerate(CG):\n", "        plot_mesh(mesh, ax[k, 0])\n", "        plot_dofmap(CGk, ax[k, 0])\n", "        ax[k, 0].set_title(\"CG \" + str(k + 1) + \" DofMap\", fontsize=30)\n", "    for (k, (CGk, dofmap_restriction_CGk)) in enumerate(zip(CG, dofmap_restriction_CG[cell_restriction_subset])):\n", "        plot_mesh_tags(mesh, cell_restriction_subset, ax[k, 1])\n", "        plot_dofmap_restriction(CGk, dofmap_restriction_CGk, ax[k, 1])\n", "        ax[k, 1].set_title(\"CG \" + str(k + 1) + \" DofMapRestriction\", fontsize=30)"]}, {"cell_type": "markdown", "id": "408fbf4e", "metadata": {}, "source": ["Assert that DOFs are at the expected locations"]}, {"cell_type": "code", "execution_count": null, "id": "d1845644", "metadata": {}, "outputs": [], "source": ["CG_1 = CG[0]\n", "dofmap_restriction_CG_1 = dofmap_restriction_CG[cell_restriction_subset][0]\n", "assert count_dofs(dofmap_restriction_CG_1, mesh.comm) == 5\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "c35c318b", "metadata": {}, "outputs": [], "source": ["CG_2 = CG[1]\n", "dofmap_restriction_CG_2 = dofmap_restriction_CG[cell_restriction_subset][1]\n", "assert count_dofs(dofmap_restriction_CG_2, mesh.comm) == 12\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 2 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, np.pi, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 4 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 5 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 5 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 7 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 9 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "8b01527b", "metadata": {}, "outputs": [], "source": ["CG_3 = CG[2]\n", "dofmap_restriction_CG_3 = dofmap_restriction_CG[cell_restriction_subset][2]\n", "assert count_dofs(dofmap_restriction_CG_3, mesh.comm) == 22\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 2 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, np.pi, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 4 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 5 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 2 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, np.pi, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 4 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 5 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 5 * np.pi / 6, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 7 * np.pi / 6, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 9 * np.pi / 6, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 2 * np.pi / 3 + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.pi - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.pi + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 4 * np.pi / 3 - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 4 * np.pi / 3 + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 5 * np.pi / 3 - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1"]}, {"cell_type": "markdown", "id": "7fef16c8", "metadata": {}, "source": ["Compare DOFs for che case of facet restriction equal to one on the entire domain. Note how there is no difference for $k=1, 2$, but the cases $k=3$ differ (the DofMapRestriction does not have the DOF at the cell center)."]}, {"cell_type": "code", "execution_count": null, "id": "c6c4d68a", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    _, ax = plt.subplots(3, 2, figsize=(20, 30))\n", "    assert isinstance(ax, np.ndarray)\n", "    for (k, CGk) in enumerate(CG):\n", "        plot_mesh(mesh, ax[k, 0])\n", "        plot_dofmap(CG[k], ax[k, 0])\n", "        ax[k, 0].set_title(\"CG \" + str(k + 1) + \" DofMap\", fontsize=30)\n", "    for (k, (CGk, dofmap_restriction_CGk)) in enumerate(zip(CG, dofmap_restriction_CG[facet_restriction_all])):\n", "        plot_mesh_tags(mesh, facet_restriction_all, ax[k, 1])\n", "        plot_dofmap_restriction(CGk, dofmap_restriction_CGk, ax[k, 1])\n", "        ax[k, 1].set_title(\"CG \" + str(k + 1) + \" DofMapRestriction\", fontsize=30)"]}, {"cell_type": "markdown", "id": "d239e075", "metadata": {}, "source": ["Assert that DOFs are at the expected locations"]}, {"cell_type": "code", "execution_count": null, "id": "a94fe793", "metadata": {}, "outputs": [], "source": ["CG_1 = CG[0]\n", "dofmap_restriction_CG_1 = dofmap_restriction_CG[facet_restriction_all][0]\n", "assert count_dofs(dofmap_restriction_CG_1, mesh.comm) == 7\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 0, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "a9b5fa38", "metadata": {}, "outputs": [], "source": ["CG_2 = CG[1]\n", "dofmap_restriction_CG_2 = dofmap_restriction_CG[facet_restriction_all][1]\n", "assert count_dofs(dofmap_restriction_CG_2, mesh.comm) == 19\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 0, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 2 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, np.pi, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 4 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 5 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 0, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 3 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 5 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 7 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 9 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 11 * np.pi / 6, CG_2, dofmap_restriction_CG_2)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "36cc163b", "metadata": {}, "outputs": [], "source": ["CG_3 = CG[2]\n", "dofmap_restriction_CG_3 = dofmap_restriction_CG[facet_restriction_all][2]\n", "assert count_dofs(dofmap_restriction_CG_3, mesh.comm) == 31\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 0, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 2 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, np.pi, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 4 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 5 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 0, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 2 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, np.pi, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 4 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 5 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 0, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.pi / 3 - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.pi / 3 + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 2 * np.pi / 3 - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 2 * np.pi / 3 + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.pi - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.pi + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 4 * np.pi / 3 - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 4 * np.pi / 3 + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 5 * np.pi / 3 - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, 5 * np.pi / 3 + np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(7) / 3, np.pi - np.arctan(np.sqrt(3) / 5), CG_3, dofmap_restriction_CG_3)) == 1"]}, {"cell_type": "markdown", "id": "e3be551f", "metadata": {}, "source": ["Compare DOFs for che case of facet restriction equal to one on a subset of the domain. Note how the DofMapRestriction has only a subset of the DOFs of the DofMap, and properly renumbers them."]}, {"cell_type": "code", "execution_count": null, "id": "2baacad2", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    _, ax = plt.subplots(3, 2, figsize=(20, 30))\n", "    assert isinstance(ax, np.ndarray)\n", "    for (k, CGk) in enumerate(CG):\n", "        plot_mesh(mesh, ax[k, 0])\n", "        plot_dofmap(CG[k], ax[k, 0])\n", "        ax[k, 0].set_title(\"CG \" + str(k + 1) + \" DofMap\", fontsize=30)\n", "    for (k, (CGk, dofmap_restriction_CGk)) in enumerate(zip(CG, dofmap_restriction_CG[facet_restriction_subset])):\n", "        plot_mesh_tags(mesh, facet_restriction_subset, ax[k, 1])\n", "        plot_dofmap_restriction(CGk, dofmap_restriction_CGk, ax[k, 1])\n", "        ax[k, 1].set_title(\"CG \" + str(k + 1) + \" DofMapRestriction\", fontsize=30)"]}, {"cell_type": "markdown", "id": "d513af37", "metadata": {}, "source": ["Assert that DOFs are at the expected locations"]}, {"cell_type": "code", "execution_count": null, "id": "fdeff253", "metadata": {}, "outputs": [], "source": ["CG_1 = CG[0]\n", "dofmap_restriction_CG_1 = dofmap_restriction_CG[facet_restriction_subset][0]\n", "assert count_dofs(dofmap_restriction_CG_1, mesh.comm) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, CG_1, dofmap_restriction_CG_1)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "eb207027", "metadata": {}, "outputs": [], "source": ["CG_2 = CG[1]\n", "dofmap_restriction_CG_2 = dofmap_restriction_CG[facet_restriction_subset][1]\n", "assert count_dofs(dofmap_restriction_CG_2, mesh.comm) == 5\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 2 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 5 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, CG_2, dofmap_restriction_CG_2)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "f8993ddd", "metadata": {}, "outputs": [], "source": ["CG_3 = CG[2]\n", "dofmap_restriction_CG_3 = dofmap_restriction_CG[facet_restriction_subset][2]\n", "assert count_dofs(dofmap_restriction_CG_3, mesh.comm) == 7\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 2 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1 / 3, 5 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 2 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    2 / 3, 5 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, CG_3, dofmap_restriction_CG_3)) == 1"]}, {"cell_type": "markdown", "id": "1332db27", "metadata": {}, "source": ["### Discontinuous Galerkin spaces\n", "\n", "Define Discontinuous Galerkin spaces of order $k = 0, 1, 2$, and plot the associated DofMap. This spaces will be used in combination with a cell restriction, as DG DOFs are associated to cells."]}, {"cell_type": "code", "execution_count": null, "id": "27793636", "metadata": {}, "outputs": [], "source": ["DG = [dolfinx.fem.functionspace(mesh, (\"Discontinuous Lagrange\", k)) for k in (0, 1, 2)]"]}, {"cell_type": "code", "execution_count": null, "id": "c5ff6900", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    _, ax = plt.subplots(3, 1, figsize=(10, 30))\n", "    assert isinstance(ax, np.ndarray)\n", "    for (k, DGk) in enumerate(DG):\n", "        plot_mesh(mesh, ax[k])\n", "        plot_dofmap(DGk, ax[k])\n", "        ax[k].set_title(\"DG \" + str(k) + \" DofMap\", fontsize=30)"]}, {"cell_type": "markdown", "id": "ada139d8", "metadata": {}, "source": ["Define Discontinuous Galerkin Trace spaces of order $k = 0, 1, 2, 3$, and plot the associated DofMap. This spaces will be used in combination with a facet restriction, as DGT DOFs are associated to facets."]}, {"cell_type": "code", "execution_count": null, "id": "c6b4e96f", "metadata": {}, "outputs": [], "source": ["# PYTEST_XFAIL: Temporarily broken due to lack of suport for DGT in basix\n", "DGT = [dolfinx.fem.functionspace(mesh, (\"Discontinuous Lagrange Trace\", k)) for k in (0, 1, 2)]"]}, {"cell_type": "code", "execution_count": null, "id": "bda45d58", "metadata": {}, "outputs": [], "source": ["# PYTEST_XFAIL: Temporarily broken due to lack of suport for DGT in basix\n", "if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    _, ax = plt.subplots(3, 1, figsize=(10, 30))\n", "    assert isinstance(ax, np.ndarray)\n", "    for (k, DGTk) in enumerate(DGT):\n", "        plot_mesh(mesh, ax[k])\n", "        plot_dofmap(DGTk, ax[k])\n", "        ax[k].set_title(\"DGT \" + str(k) + \" DofMap\\n\", fontsize=30)"]}, {"cell_type": "markdown", "id": "dc7bab4b", "metadata": {}, "source": ["Define DofMapRestriction objects associated to the Discontinuos Galerkin FE spaces, for all cell restrictions"]}, {"cell_type": "code", "execution_count": null, "id": "3e87a436", "metadata": {}, "outputs": [], "source": ["dofmap_restriction_DG: dict[\n", "    dolfinx.mesh.MeshTags, list[multiphenicsx.fem.DofMapRestriction]] = dict()\n", "for restriction in cell_restrictions:\n", "    dofmap_restriction_DG[restriction] = list()\n", "    for DGk in DG:\n", "        restrict_DGk = dolfinx.fem.locate_dofs_topological(\n", "            DGk, restriction.dim, restriction.indices[restriction.values == 1])\n", "        dofmap_restriction_DG[restriction].append(\n", "            multiphenicsx.fem.DofMapRestriction(DGk.dofmap, restrict_DGk))"]}, {"cell_type": "markdown", "id": "930b1d2d", "metadata": {}, "source": ["Define DofMapRestriction objects associated to the Discontinuos Galerkin Trace FE spaces, for all facet restrictions"]}, {"cell_type": "code", "execution_count": null, "id": "790f2735", "metadata": {}, "outputs": [], "source": ["# PYTEST_XFAIL: Temporarily broken due to lack of suport for DGT in basix\n", "dofmap_restriction_DGT: dict[\n", "    dolfinx.mesh.MeshTags, list[multiphenicsx.fem.DofMapRestriction]] = dict()\n", "for restriction in facet_restrictions:\n", "    dofmap_restriction_DGT[restriction] = list()\n", "    for DGTk in DGT:\n", "        restrict_DGTk = dolfinx.fem.locate_dofs_topological(\n", "            DGTk, restriction.dim, restriction.indices[restriction.values == 1])\n", "        dofmap_restriction_DGT[restriction].append(\n", "            multiphenicsx.fem.DofMapRestriction(DGTk.dofmap, restrict_DGTk))"]}, {"cell_type": "markdown", "id": "4568dc71", "metadata": {}, "source": ["Compare DOFs for the case of cell restriction equal to one on the entire domain. There is indeed no difference."]}, {"cell_type": "code", "execution_count": null, "id": "3f1cca97", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    _, ax = plt.subplots(3, 2, figsize=(20, 30))\n", "    assert isinstance(ax, np.ndarray)\n", "    for (k, DGk) in enumerate(DG):\n", "        plot_mesh(mesh, ax[k, 0])\n", "        plot_dofmap(DGk, ax[k, 0])\n", "        ax[k, 0].set_title(\"DG \" + str(k) + \" DofMap\", fontsize=30)\n", "    for (k, (DGk, dofmap_restriction_DGk)) in enumerate(zip(DG, dofmap_restriction_DG[cell_restriction_all])):\n", "        plot_mesh_tags(mesh, cell_restriction_all, ax[k, 1])\n", "        plot_dofmap_restriction(DGk, dofmap_restriction_DGk, ax[k, 1])\n", "        ax[k, 1].set_title(\"DG \" + str(k) + \" DofMapRestriction\", fontsize=30)"]}, {"cell_type": "markdown", "id": "bc1129a6", "metadata": {}, "source": ["Assert that DOFs are at the expected locations"]}, {"cell_type": "code", "execution_count": null, "id": "0884a3ab", "metadata": {}, "outputs": [], "source": ["DG_0 = DG[0]\n", "dofmap_restriction_DG_0 = dofmap_restriction_DG[cell_restriction_all][0]\n", "assert count_dofs(dofmap_restriction_DG_0, mesh.comm) == 6\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, np.pi / 6, DG_0, dofmap_restriction_DG_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 3 * np.pi / 6, DG_0, dofmap_restriction_DG_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 5 * np.pi / 6, DG_0, dofmap_restriction_DG_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 7 * np.pi / 6, DG_0, dofmap_restriction_DG_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 9 * np.pi / 6, DG_0, dofmap_restriction_DG_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 11 * np.pi / 6, DG_0, dofmap_restriction_DG_0)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "1a7fdcd9", "metadata": {}, "outputs": [], "source": ["DG_1 = DG[1]\n", "dofmap_restriction_DG_1 = dofmap_restriction_DG[cell_restriction_all][1]\n", "assert count_dofs(dofmap_restriction_DG_1, mesh.comm) == 18\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, DG_1, dofmap_restriction_DG_1)) == 6\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 0, DG_1, dofmap_restriction_DG_1)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi / 3, DG_1, dofmap_restriction_DG_1)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, DG_1, dofmap_restriction_DG_1)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, DG_1, dofmap_restriction_DG_1)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, DG_1, dofmap_restriction_DG_1)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, DG_1, dofmap_restriction_DG_1)) == 2"]}, {"cell_type": "code", "execution_count": null, "id": "991b3d1b", "metadata": {}, "outputs": [], "source": ["DG_2 = DG[2]\n", "dofmap_restriction_DG_2 = dofmap_restriction_DG[cell_restriction_all][2]\n", "assert count_dofs(dofmap_restriction_DG_2, mesh.comm) == 36\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, DG_2, dofmap_restriction_DG_2)) == 6\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 0, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 2 * np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, np.pi, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 4 * np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 5 * np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 0, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, np.pi / 6, DG_2, dofmap_restriction_DG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 3 * np.pi / 6, DG_2, dofmap_restriction_DG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 5 * np.pi / 6, DG_2, dofmap_restriction_DG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 7 * np.pi / 6, DG_2, dofmap_restriction_DG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 9 * np.pi / 6, DG_2, dofmap_restriction_DG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 11 * np.pi / 6, DG_2, dofmap_restriction_DG_2)) == 1"]}, {"cell_type": "markdown", "id": "971bd7ab", "metadata": {}, "source": ["Compare DOFs for che case of cell restriction equal to one on a subset of the domain. Note how the DofMapRestriction has only a subset of the DOFs of the DofMap, and properly renumbers them. Note also that the number of DOFs at the same physical location might be different between DofMap and DofMapRestriction (see e.g. the center of the hexagon)."]}, {"cell_type": "code", "execution_count": null, "id": "83af20d6", "metadata": {}, "outputs": [], "source": ["if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    _, ax = plt.subplots(3, 2, figsize=(20, 30))\n", "    assert isinstance(ax, np.ndarray)\n", "    for (k, DGk) in enumerate(DG):\n", "        plot_mesh(mesh, ax[k, 0])\n", "        plot_dofmap(DGk, ax[k, 0])\n", "        ax[k, 0].set_title(\"DG \" + str(k) + \" DofMap\", fontsize=30)\n", "    for (k, (DGk, dofmap_restriction_DGk)) in enumerate(zip(DG, dofmap_restriction_DG[cell_restriction_subset])):\n", "        plot_mesh_tags(mesh, cell_restriction_subset, ax[k, 1])\n", "        plot_dofmap_restriction(DGk, dofmap_restriction_DGk, ax[k, 1])\n", "        ax[k, 1].set_title(\"DG \" + str(k) + \" DofMapRestriction\", fontsize=30)"]}, {"cell_type": "markdown", "id": "2de54c54", "metadata": {}, "source": ["Assert that DOFs are at the expected locations"]}, {"cell_type": "code", "execution_count": null, "id": "3136ea99", "metadata": {}, "outputs": [], "source": ["DG_0 = DG[0]\n", "dofmap_restriction_DG_0 = dofmap_restriction_DG[cell_restriction_subset][0]\n", "assert count_dofs(dofmap_restriction_DG_0, mesh.comm) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 5 * np.pi / 6, DG_0, dofmap_restriction_DG_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 7 * np.pi / 6, DG_0, dofmap_restriction_DG_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 3, 9 * np.pi / 6, DG_0, dofmap_restriction_DG_0)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "b0e7523e", "metadata": {}, "outputs": [], "source": ["DG_1 = DG[1]\n", "dofmap_restriction_DG_1 = dofmap_restriction_DG[cell_restriction_subset][1]\n", "assert count_dofs(dofmap_restriction_DG_1, mesh.comm) == 9\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, DG_1, dofmap_restriction_DG_1)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, DG_1, dofmap_restriction_DG_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, DG_1, dofmap_restriction_DG_1)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, DG_1, dofmap_restriction_DG_1)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, DG_1, dofmap_restriction_DG_1)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "f39145ca", "metadata": {}, "outputs": [], "source": ["DG_2 = DG[2]\n", "dofmap_restriction_DG_2 = dofmap_restriction_DG[cell_restriction_subset][2]\n", "assert count_dofs(dofmap_restriction_DG_2, mesh.comm) == 18\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, DG_2, dofmap_restriction_DG_2)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 2 * np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, np.pi, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 4 * np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 5 * np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, DG_2, dofmap_restriction_DG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 5 * np.pi / 6, DG_2, dofmap_restriction_DG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 7 * np.pi / 6, DG_2, dofmap_restriction_DG_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 9 * np.pi / 6, DG_2, dofmap_restriction_DG_2)) == 1"]}, {"cell_type": "markdown", "id": "02eaf6e1", "metadata": {}, "source": ["Compare DOFs for che case of facet restriction equal to one on the entire domain. There is indeed no difference."]}, {"cell_type": "code", "execution_count": null, "id": "f974e2d4", "metadata": {}, "outputs": [], "source": ["# PYTEST_XFAIL: Temporarily broken due to lack of suport for DGT in basix\n", "if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    _, ax = plt.subplots(3, 2, figsize=(20, 30))\n", "    assert isinstance(ax, np.ndarray)\n", "    for (k, DGTk) in enumerate(DGT):\n", "        plot_mesh(mesh, ax[k, 0])\n", "        plot_dofmap(DGTk, ax[k, 0])\n", "        ax[k, 0].set_title(\"DGT \" + str(k) + \" DofMap\\n\", fontsize=30)\n", "    for (k, (DGTk, dofmap_restriction_DGTk)) in enumerate(zip(DGT, dofmap_restriction_DGT[facet_restriction_all])):\n", "        plot_mesh_tags(mesh, facet_restriction_all, ax[k, 1])\n", "        plot_dofmap_restriction(DGTk, dofmap_restriction_DGTk, ax[k, 1])\n", "        ax[k, 1].set_title(\"DGT \" + str(k) + \" DofMapRestriction\\n\", fontsize=30)"]}, {"cell_type": "markdown", "id": "a4dc282b", "metadata": {}, "source": ["Assert that DOFs are at the expected locations"]}, {"cell_type": "code", "execution_count": null, "id": "1e5a4dd1", "metadata": {}, "outputs": [], "source": ["# PYTEST_XFAIL: Temporarily broken due to lack of suport for DGT in basix\n", "DGT_0 = DGT[0]\n", "dofmap_restriction_DGT_0 = dofmap_restriction_DGT[facet_restriction_all][0]\n", "assert count_dofs(dofmap_restriction_DGT_0, mesh.comm) == 12\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 0, DGT_0, dofmap_restriction_DGT_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, np.pi / 3, DGT_0, dofmap_restriction_DGT_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 2 * np.pi / 3, DGT_0, dofmap_restriction_DGT_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, np.pi, DGT_0, dofmap_restriction_DGT_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 4 * np.pi / 3, DGT_0, dofmap_restriction_DGT_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 5 * np.pi / 3, DGT_0, dofmap_restriction_DGT_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, np.pi / 6, DGT_0, dofmap_restriction_DGT_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 3 * np.pi / 6, DGT_0, dofmap_restriction_DGT_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 5 * np.pi / 6, DGT_0, dofmap_restriction_DGT_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 7 * np.pi / 6, DGT_0, dofmap_restriction_DGT_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 9 * np.pi / 6, DGT_0, dofmap_restriction_DGT_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 11 * np.pi / 6, DGT_0, dofmap_restriction_DGT_0)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "608cd92a", "metadata": {}, "outputs": [], "source": ["# PYTEST_XFAIL: Temporarily broken due to lack of suport for DGT in basix\n", "DGT_1 = DGT[1]\n", "dofmap_restriction_DGT_1 = dofmap_restriction_DGT[facet_restriction_all][1]\n", "assert count_dofs(dofmap_restriction_DGT_1, mesh.comm) == 24\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, DGT_1, dofmap_restriction_DGT_1)) == 6\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 0, DGT_1, dofmap_restriction_DGT_1)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi / 3, DGT_1, dofmap_restriction_DGT_1)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, DGT_1, dofmap_restriction_DGT_1)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, DGT_1, dofmap_restriction_DGT_1)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, DGT_1, dofmap_restriction_DGT_1)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, DGT_1, dofmap_restriction_DGT_1)) == 3"]}, {"cell_type": "code", "execution_count": null, "id": "693d9597", "metadata": {}, "outputs": [], "source": ["# PYTEST_XFAIL: Temporarily broken due to lack of suport for DGT in basix\n", "DGT_2 = DGT[2]\n", "dofmap_restriction_DGT_2 = dofmap_restriction_DGT[facet_restriction_all][2]\n", "assert count_dofs(dofmap_restriction_DGT_2, mesh.comm) == 36\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, DGT_2, dofmap_restriction_DGT_2)) == 6\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 0, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, np.pi / 3, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 2 * np.pi / 3, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, np.pi, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 4 * np.pi / 3, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 5 * np.pi / 3, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 0, DGT_2, dofmap_restriction_DGT_2)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi / 3, DGT_2, dofmap_restriction_DGT_2)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, DGT_2, dofmap_restriction_DGT_2)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, np.pi, DGT_2, dofmap_restriction_DGT_2)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 4 * np.pi / 3, DGT_2, dofmap_restriction_DGT_2)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, DGT_2, dofmap_restriction_DGT_2)) == 3\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, np.pi / 6, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 3 * np.pi / 6, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 5 * np.pi / 6, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 7 * np.pi / 6, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 9 * np.pi / 6, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    np.sqrt(3) / 2, 11 * np.pi / 6, DGT_2, dofmap_restriction_DGT_2)) == 1"]}, {"cell_type": "markdown", "id": "3769df4a", "metadata": {}, "source": ["Compare DOFs for che case of facet restriction equal to one on a subset of the domain. Note how the DofMapRestriction has only a subset of the DOFs of the DofMap, and properly renumbers them. Note also that the number of DOFs at the same physical location might be different between DofMap and DofMapRestriction (see e.g. the center of the hexagon)."]}, {"cell_type": "code", "execution_count": null, "id": "f3485605", "metadata": {}, "outputs": [], "source": ["# PYTEST_XFAIL: Temporarily broken due to lack of suport for DGT in basix\n", "if \"PYTEST_CURRENT_TEST\" not in os.environ:\n", "    _, ax = plt.subplots(3, 2, figsize=(20, 30))\n", "    assert isinstance(ax, np.ndarray)\n", "    for (k, DGTk) in enumerate(DGT):\n", "        plot_mesh(mesh, ax[k, 0])\n", "        plot_dofmap(DGTk, ax[k, 0])\n", "        ax[k, 0].set_title(\"DGT \" + str(k) + \" DofMap\\n\", fontsize=30)\n", "    for (k, (DGTk, dofmap_restriction_DGTk)) in enumerate(zip(DGT, dofmap_restriction_DGT[facet_restriction_subset])):\n", "        plot_mesh_tags(mesh, facet_restriction_subset, ax[k, 1])\n", "        plot_dofmap_restriction(DGTk, dofmap_restriction_DGTk, ax[k, 1])\n", "        ax[k, 1].set_title(\"DGT \" + str(k) + \" DofMapRestriction\\n\", fontsize=30)"]}, {"cell_type": "markdown", "id": "258d6943", "metadata": {}, "source": ["Assert that DOFs are at the expected locations"]}, {"cell_type": "code", "execution_count": null, "id": "7165a970", "metadata": {}, "outputs": [], "source": ["# PYTEST_XFAIL: Temporarily broken due to lack of suport for DGT in basix\n", "DGT_0 = DGT[0]\n", "dofmap_restriction_DGT_0 = dofmap_restriction_DGT[facet_restriction_subset][0]\n", "assert count_dofs(dofmap_restriction_DGT_0, mesh.comm) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 2 * np.pi / 3, DGT_0, dofmap_restriction_DGT_0)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 5 * np.pi / 3, DGT_0, dofmap_restriction_DGT_0)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "5a8940c7", "metadata": {}, "outputs": [], "source": ["# PYTEST_XFAIL: Temporarily broken due to lack of suport for DGT in basix\n", "DGT_1 = DGT[1]\n", "dofmap_restriction_DGT_1 = dofmap_restriction_DGT[facet_restriction_subset][1]\n", "assert count_dofs(dofmap_restriction_DGT_1, mesh.comm) == 4\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, DGT_1, dofmap_restriction_DGT_1)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, DGT_1, dofmap_restriction_DGT_1)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, DGT_1, dofmap_restriction_DGT_1)) == 1"]}, {"cell_type": "code", "execution_count": null, "id": "bf19599c", "metadata": {}, "outputs": [], "source": ["# PYTEST_XFAIL: Temporarily broken due to lack of suport for DGT in basix\n", "DGT_2 = DGT[2]\n", "dofmap_restriction_DGT_2 = dofmap_restriction_DGT[facet_restriction_subset][2]\n", "assert count_dofs(dofmap_restriction_DGT_2, mesh.comm) == 6\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0, 0, DGT_2, dofmap_restriction_DGT_2)) == 2\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 2 * np.pi / 3, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    0.5, 5 * np.pi / 3, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 2 * np.pi / 3, DGT_2, dofmap_restriction_DGT_2)) == 1\n", "assert len(locate_dofs_by_polar_coordinates(\n", "    1, 5 * np.pi / 3, DGT_2, dofmap_restriction_DGT_2)) == 1"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython"}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python"}}, "nbformat": 4, "nbformat_minor": 5}